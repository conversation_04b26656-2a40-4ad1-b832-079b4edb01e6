/**
 * Enhanced FAQ Functionality for CalculatorSuites
 * Provides search, filtering, and collapsible functionality
 */

class FAQEnhancement {
  constructor() {
    this.faqItems = [];
    this.currentFilter = 'all';
    this.searchTerm = '';
    this.init();
  }

  init() {
    this.setupFAQStructure();
    this.bindEvents();
    this.initializeCollapsibleFAQs();
  }

  setupFAQStructure() {
    const faqSection = document.querySelector('.calculator-faq');
    if (!faqSection) return;

    // Add search and filter controls
    const controlsHTML = `
      <div class="faq-controls">
        <div class="faq-search">
          <input type="text" placeholder="Search FAQs..." id="faq-search-input">
        </div>
        <div class="faq-filters">
          <button class="faq-filter-btn active" data-filter="all">All</button>
          <button class="faq-filter-btn" data-filter="basics">Basics</button>
          <button class="faq-filter-btn" data-filter="advanced">Advanced</button>
          <button class="faq-filter-btn" data-filter="troubleshooting">Troubleshooting</button>
        </div>
      </div>
      <div class="faq-no-results">
        <p>No FAQs found matching your search criteria.</p>
      </div>
    `;

    // Insert controls after the h2 title
    const title = faqSection.querySelector('h2');
    if (title) {
      title.insertAdjacentHTML('afterend', controlsHTML);
    }

    // Convert existing FAQ items to collapsible format
    this.convertFAQItems();
    this.collectFAQItems();
  }

  convertFAQItems() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach((item, index) => {
      const question = item.querySelector('h4');
      const content = item.innerHTML;
      
      if (question) {
        const questionText = question.textContent;
        const answerContent = content.replace(question.outerHTML, '');
        
        // Determine category based on content keywords
        const category = this.categorizeQuestion(questionText, answerContent);
        
        item.className = 'faq-item';
        item.setAttribute('data-category', category);
        item.setAttribute('data-index', index);
        
        item.innerHTML = `
          <button class="faq-question" aria-expanded="false" aria-controls="faq-answer-${index}">
            <h4>${questionText}</h4>
            <span class="faq-toggle">▼</span>
          </button>
          <div class="faq-answer" id="faq-answer-${index}">
            ${answerContent}
          </div>
        `;
      }
    });
  }

  categorizeQuestion(question, content) {
    const questionLower = question.toLowerCase();
    const contentLower = content.toLowerCase();
    
    // Basic questions
    if (questionLower.includes('what is') || 
        questionLower.includes('how does') || 
        questionLower.includes('basic') ||
        questionLower.includes('beginner')) {
      return 'basics';
    }
    
    // Advanced questions
    if (questionLower.includes('strategy') || 
        questionLower.includes('optimization') || 
        questionLower.includes('advanced') ||
        questionLower.includes('step-up') ||
        questionLower.includes('prepayment') ||
        contentLower.includes('scenario') ||
        contentLower.includes('comparison')) {
      return 'advanced';
    }
    
    // Troubleshooting questions
    if (questionLower.includes('why') || 
        questionLower.includes('problem') || 
        questionLower.includes('issue') ||
        questionLower.includes('accurate') ||
        questionLower.includes('different') ||
        questionLower.includes('error')) {
      return 'troubleshooting';
    }
    
    return 'basics'; // Default category
  }

  collectFAQItems() {
    this.faqItems = Array.from(document.querySelectorAll('.faq-item')).map(item => ({
      element: item,
      question: item.querySelector('h4').textContent.toLowerCase(),
      content: item.querySelector('.faq-answer').textContent.toLowerCase(),
      category: item.getAttribute('data-category')
    }));
  }

  bindEvents() {
    // Search functionality
    const searchInput = document.getElementById('faq-search-input');
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        this.searchTerm = e.target.value.toLowerCase();
        this.filterFAQs();
      });
    }

    // Filter buttons
    const filterButtons = document.querySelectorAll('.faq-filter-btn');
    filterButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        // Update active state
        filterButtons.forEach(btn => btn.classList.remove('active'));
        e.target.classList.add('active');
        
        this.currentFilter = e.target.getAttribute('data-filter');
        this.filterFAQs();
      });
    });

    // FAQ toggle functionality
    document.addEventListener('click', (e) => {
      if (e.target.closest('.faq-question')) {
        e.preventDefault();
        this.toggleFAQ(e.target.closest('.faq-item'));
      }
    });
  }

  initializeCollapsibleFAQs() {
    // Close all FAQs initially
    const faqItems = document.querySelectorAll('.faq-item');
    faqItems.forEach(item => {
      item.classList.remove('active');
      const button = item.querySelector('.faq-question');
      if (button) {
        button.setAttribute('aria-expanded', 'false');
      }
    });
  }

  toggleFAQ(faqItem) {
    const isActive = faqItem.classList.contains('active');
    const button = faqItem.querySelector('.faq-question');
    
    if (isActive) {
      faqItem.classList.remove('active');
      button.setAttribute('aria-expanded', 'false');
    } else {
      faqItem.classList.add('active');
      button.setAttribute('aria-expanded', 'true');
    }
  }

  filterFAQs() {
    let visibleCount = 0;
    
    this.faqItems.forEach(item => {
      const matchesSearch = this.searchTerm === '' || 
        item.question.includes(this.searchTerm) || 
        item.content.includes(this.searchTerm);
      
      const matchesFilter = this.currentFilter === 'all' || 
        item.category === this.currentFilter;
      
      if (matchesSearch && matchesFilter) {
        item.element.style.display = 'block';
        visibleCount++;
      } else {
        item.element.style.display = 'none';
      }
    });

    // Show/hide no results message
    const noResults = document.querySelector('.faq-no-results');
    if (noResults) {
      noResults.style.display = visibleCount === 0 ? 'block' : 'none';
    }

    // Hide category headers if no items in category are visible
    this.updateCategoryVisibility();
  }

  updateCategoryVisibility() {
    const categories = document.querySelectorAll('.faq-category');
    categories.forEach(category => {
      const visibleItems = category.querySelectorAll('.faq-item[style*="block"], .faq-item:not([style*="none"])');
      const hasVisibleItems = Array.from(visibleItems).some(item => 
        item.style.display !== 'none'
      );
      
      category.style.display = hasVisibleItems ? 'block' : 'none';
    });
  }

  // Public method to expand all FAQs
  expandAll() {
    const faqItems = document.querySelectorAll('.faq-item');
    faqItems.forEach(item => {
      if (item.style.display !== 'none') {
        item.classList.add('active');
        const button = item.querySelector('.faq-question');
        if (button) {
          button.setAttribute('aria-expanded', 'true');
        }
      }
    });
  }

  // Public method to collapse all FAQs
  collapseAll() {
    const faqItems = document.querySelectorAll('.faq-item');
    faqItems.forEach(item => {
      item.classList.remove('active');
      const button = item.querySelector('.faq-question');
      if (button) {
        button.setAttribute('aria-expanded', 'false');
      }
    });
  }
}

// Initialize FAQ enhancement when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new FAQEnhancement();
});

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = FAQEnhancement;
}
