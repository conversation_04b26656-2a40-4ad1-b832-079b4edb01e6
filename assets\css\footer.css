/* Footer styles for Calculator Suites */

/* Footer */
.site-footer {
  background-color: var(--neutral-800);
  color: var(--neutral-300);
  padding: 3rem 0 1.5rem;
  width: 100%;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
}

.footer-column h4 {
  color: white;
  margin-bottom: 1.25rem;
  font-size: var(--text-lg);
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.75rem;
}

.footer-links a {
  color: var(--neutral-300);
  transition: color 0.2s ease;
  display: inline-block;
  padding: 0.25rem 0;
}

.footer-links a:hover {
  color: white;
  text-decoration: none;
}

.footer-bottom {
  margin-top: 3rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--neutral-700);
  text-align: center;
  color: var(--neutral-500);
  font-size: var(--text-sm);
}

/* Mobile footer styles */
@media (max-width: 767px) {
  .site-footer {
    padding: 2rem 0 1rem;
  }
  
  .footer-grid {
    display: block;
  }
  
  .footer-column {
    margin-bottom: 2rem;
  }
  
  .footer-column h4 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }
  
  .footer-links li {
    margin-bottom: 0.5rem;
  }
  
  .footer-bottom {
    margin-top: 1.5rem;
    padding-top: 1rem;
  }
}
