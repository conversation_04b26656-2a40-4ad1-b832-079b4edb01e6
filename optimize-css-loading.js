#!/usr/bin/env node

/**
 * CSS Loading Optimization Script
 * Optimizes CSS loading for better LCP performance across all calculator pages
 */

const fs = require('fs');
const path = require('path');

// Critical CSS template
const criticalCSS = `
  <!-- Inline Critical CSS -->
  <style>
    /* Critical CSS - Above the fold styles */
    :root {
      --primary-color: #4361ee;
      --primary-light: #4895ef;
      --primary-dark: #3a0ca3;
      --neutral-100: #f8f9fa;
      --neutral-200: #e9ecef;
      --neutral-800: #343a40;
      --neutral-900: #212529;
      --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    *, *::before, *::after { box-sizing: border-box; margin: 0; padding: 0; }
    
    body {
      font-family: var(--font-primary);
      font-size: 1rem;
      line-height: 1.5;
      color: var(--neutral-800);
      background-color: var(--neutral-100);
    }
    
    .site-header {
      background-color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      position: sticky;
      top: 0;
      z-index: 100;
    }
    
    .nav-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .logo-text {
      font-family: var(--font-heading);
      font-size: 1.5rem;
      font-weight: 700;
      background: linear-gradient(45deg, var(--primary-color), #f72585);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .nav-menu { display: none; list-style: none; }
    .mobile-menu-toggle { display: block; background: none; border: none; cursor: pointer; padding: 0.5rem; }
    
    @media (min-width: 768px) {
      .mobile-menu-toggle { display: none; }
      .nav-menu { display: flex; }
      .logo-text { font-size: 1.75rem; }
    }
    
    .container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
    .breadcrumb-container { background-color: #f8f9fa; padding: 0.75rem 0; }
    h1 { font-family: var(--font-heading); font-size: 2.25rem; font-weight: 600; margin-bottom: 1rem; }
    
    /* Hide non-critical content initially */
    .calculator-container, .site-footer { opacity: 0; }
  </style>`;

// Async CSS loading template
const asyncCSSTemplate = (basePath = '') => `
  <!-- Load Google Fonts asynchronously -->
  <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@500;600;700&display=swap"></noscript>

  <!-- Load non-critical CSS asynchronously -->
  <link rel="preload" href="${basePath}assets/css/main.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <link rel="preload" href="${basePath}assets/css/calculator.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <link rel="preload" href="${basePath}assets/css/responsive.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <link rel="preload" href="${basePath}assets/css/footer.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  
  <!-- Fallback for browsers without JS -->
  <noscript>
    <link rel="stylesheet" href="${basePath}assets/css/main.css">
    <link rel="stylesheet" href="${basePath}assets/css/calculator.css">
    <link rel="stylesheet" href="${basePath}assets/css/responsive.css">
    <link rel="stylesheet" href="${basePath}assets/css/footer.css">
  </noscript>`;

// Script to show hidden content
const showContentScript = `
  <!-- Script to show content after CSS loads -->
  <script>
    // Show hidden content after CSS loads
    function showContent() {
      const hiddenElements = document.querySelectorAll('.calculator-container, .site-footer');
      hiddenElements.forEach(el => {
        el.style.opacity = '1';
        el.style.transition = 'opacity 0.3s ease-in-out';
      });
    }
    
    // Wait for CSS to load
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(showContent, 100);
      });
    } else {
      setTimeout(showContent, 100);
    }
  </script>`;

// Font preload template
const fontPreloadTemplate = `
  <!-- Preload key font files -->
  <link rel="preload" href="https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiJ-Ek-_EeA.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="https://fonts.gstatic.com/s/poppins/v20/pxiByp8kv8JHgFVrLEj6Z1xlFd2JQEk.woff2" as="font" type="font/woff2" crossorigin>`;

// Function to get all HTML files in calculator directories
function getCalculatorFiles() {
  const directories = ['tax', 'investment', 'loan', 'health', 'discount'];
  const files = [];
  
  directories.forEach(dir => {
    if (fs.existsSync(dir)) {
      const dirFiles = fs.readdirSync(dir)
        .filter(file => file.endsWith('.html'))
        .map(file => path.join(dir, file));
      files.push(...dirFiles);
    }
  });
  
  return files;
}

console.log('CSS Loading Optimization Script');
console.log('This script optimizes CSS loading for better LCP performance');
console.log('Files to optimize:', getCalculatorFiles());
