<svg width="400" height="250" viewBox="0 0 400 250" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="loanGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
    <style>
      .title-text { font-family: 'Poppins', sans-serif; font-weight: 700; font-size: 20px; fill: white; }
      .category-text { font-family: 'Poppins', sans-serif; font-weight: 500; font-size: 12px; fill: white; opacity: 0.9; text-transform: uppercase; letter-spacing: 1px; }
      .brand-text { font-family: 'Poppins', sans-serif; font-weight: 500; font-size: 14px; fill: white; opacity: 0.8; }
    </style>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="250" rx="12" fill="url(#loanGradient)"/>
  
  <!-- Decorative circles -->
  <circle cx="320" cy="50" r="2" fill="white" opacity="0.3"/>
  <circle cx="360" cy="80" r="1.5" fill="white" opacity="0.2"/>
  <circle cx="340" cy="120" r="1" fill="white" opacity="0.4"/>
  
  <!-- Home icon -->
  <g transform="translate(340, 20)" fill="white" opacity="0.3">
    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
  </g>
  
  <!-- Content -->
  <text x="30" y="50" class="category-text">Loan Planning</text>
  <text x="30" y="80" class="title-text">Home Loan EMI</text>
  <text x="30" y="105" class="title-text">Planning Guide</text>
  
  <!-- Brand -->
  <text x="270" y="220" class="brand-text">CalculatorSuites</text>
</svg>
