/* Search Intent Optimization Styles */

/* Quick Navigation */
.quick-nav {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px 20px;
    margin: 20px 0;
    border-left: 4px solid var(--primary-color);
}

.quick-nav h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: var(--dark-color);
}

.quick-nav ul {
    margin: 0;
    padding-left: 20px;
}

.quick-nav li {
    margin-bottom: 8px;
}

.quick-nav a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.quick-nav a:hover {
    text-decoration: underline;
}

/* Formula Box */
.formula-box {
    background-color: #f0f7ff;
    border-radius: 8px;
    padding: 15px 20px;
    margin: 15px 0;
    text-align: center;
    border: 1px solid #d0e3ff;
}

.formula-box p {
    font-size: 1.2em;
    margin: 0;
}

/* Info Box */
.info-box {
    background-color: #f0f7ff;
    border-radius: 8px;
    padding: 15px 20px;
    margin: 20px 0;
    border-left: 4px solid #4a6cf7;
}

.info-box h4 {
    margin-top: 0;
    color: #4a6cf7;
}

.info-box p:last-child {
    margin-bottom: 0;
}

/* Note Box */
.note-box {
    background-color: #fff8e6;
    border-radius: 8px;
    padding: 15px 20px;
    margin: 20px 0;
    border-left: 4px solid #ffc107;
}

.note-box p {
    margin: 0;
}

/* BMI Categories Table */
.bmi-categories-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.bmi-categories-table th {
    background-color: var(--primary-color);
    color: white;
    text-align: left;
    padding: 12px 15px;
}

.bmi-categories-table td {
    padding: 10px 15px;
    border-bottom: 1px solid #e0e0e0;
}

.bmi-categories-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

.bmi-categories-table tr:hover {
    background-color: #f0f7ff;
}

/* Comparison Table */
.comparison-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.comparison-table th {
    background-color: var(--primary-color);
    color: white;
    text-align: left;
    padding: 12px 10px;
    font-size: 0.9em;
}

.comparison-table td {
    padding: 10px;
    border-bottom: 1px solid #e0e0e0;
    font-size: 0.9em;
}

.comparison-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

.comparison-table tr:hover {
    background-color: #f0f7ff;
}

.comparison-table .check {
    color: #28a745;
    font-weight: bold;
}

.comparison-table .cross {
    color: #dc3545;
    font-weight: bold;
}

/* CTA Box */
.cta-box {
    background-color: #f0f7ff;
    border-radius: 8px;
    padding: 20px;
    margin: 30px 0;
    text-align: center;
    border: 1px solid #d0e3ff;
}

.cta-box h3 {
    margin-top: 0;
    color: var(--dark-color);
}

.cta-box .primary-btn {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    margin-top: 15px;
    transition: background-color 0.3s;
}

.cta-box .primary-btn:hover {
    background-color: #3a5bd9;
}

/* Enhanced FAQ Items */
.faq-item {
    margin-bottom: 25px;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 20px;
}

.faq-item h3 {
    color: var(--dark-color);
    margin-bottom: 10px;
    cursor: pointer;
    position: relative;
    padding-right: 30px;
}

.faq-item h3:after {
    content: "+";
    position: absolute;
    right: 0;
    top: 0;
    font-size: 1.2em;
    color: var(--primary-color);
}

.faq-item p {
    margin-top: 10px;
    line-height: 1.6;
}

/* Sidebar Styles */
.quick-calculator {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.mini-form .form-group {
    margin-bottom: 12px;
}

.mini-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.mini-form input {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

.mini-form .calculate-btn {
    width: 100%;
    padding: 8px 0;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.comparison-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.comparison-list li {
    padding: 8px 0;
    border-bottom: 1px solid #e0e0e0;
}

.comparison-list li:last-child {
    border-bottom: none;
}

.text-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    display: inline-block;
    margin-top: 10px;
}

.text-link:hover {
    text-decoration: underline;
}

.popular-questions {
    list-style: none;
    padding: 0;
    margin: 0;
}

.popular-questions li {
    padding: 8px 0;
    border-bottom: 1px solid #e0e0e0;
}

.popular-questions li:last-child {
    border-bottom: none;
}

.popular-questions a {
    color: var(--primary-color);
    text-decoration: none;
}

.popular-questions a:hover {
    text-decoration: underline;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
}

.action-buttons button {
    flex: 1;
    min-width: 120px;
    padding: 8px 15px;
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s;
}

.action-buttons button:hover {
    background-color: #e9ecef;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
    }
    
    .comparison-table th,
    .comparison-table td {
        padding: 8px 5px;
        font-size: 0.8em;
    }
}
