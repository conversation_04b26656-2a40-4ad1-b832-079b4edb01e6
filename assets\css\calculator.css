/* Calculator-specific styles */

/* Calculator Container */
.calculator-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
  width: 100%;
  box-sizing: border-box;
}

.calculator-container h2 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--neutral-200);
  font-size: 1.5rem;
  word-wrap: break-word;
}

/* Form Elements */
.form-group {
  margin-bottom: 1.25rem;
  width: 100%;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--neutral-700);
}

.form-group input[type="number"],
.form-group input[type="text"],
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--neutral-300);
  border-radius: 0.25rem;
  font-size: var(--text-base);
  transition: border-color 0.2s ease;
  box-sizing: border-box;
  -webkit-appearance: none;
  /* Removes default styling on iOS */
  appearance: none;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

/* Custom select arrow for better mobile appearance */
select {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23495057' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  padding-right: 2.5rem;
}

.input-group {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  /* Allow wrapping on very small screens */
}

.input-group input,
.input-group select {
  flex: 1;
  min-width: 120px;
  /* Prevent inputs from becoming too narrow */
}

.calculate-btn {
  display: block;
  width: 100%;
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-size: var(--text-base);
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  -webkit-tap-highlight-color: transparent;
  /* Remove tap highlight on mobile */
  margin-top: 1.5rem;
}

.calculate-btn:hover {
  background-color: var(--primary-dark);
}

/* Active state for better mobile feedback */
.calculate-btn:active {
  transform: translateY(1px);
  background-color: var(--primary-dark);
}

/* Unit Toggle */
.unit-toggle {
  display: flex;
  margin-bottom: 1.5rem;
  background-color: var(--neutral-100);
  border-radius: 0.25rem;
  padding: 0.25rem;
}

.unit-toggle label {
  flex: 1;
  text-align: center;
  padding: 0.5rem;
  cursor: pointer;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
}

.unit-toggle input[type="radio"] {
  position: absolute;
  opacity: 0;
}

.unit-toggle input[type="radio"]:checked+label {
  background-color: var(--primary-color);
  color: white;
}

/* Results Display */
.results {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--neutral-300);
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.results h3 {
  margin-bottom: 1.5rem;
  color: var(--primary-color);
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
  position: relative;
}

.results h3::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 2px;
}

/* Modern Result Cards */
.result-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.result-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.result-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.result-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.result-card.highlight {
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.1), rgba(67, 97, 238, 0.05));
  border: 2px solid var(--primary-color);
}

.result-card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  color: white;
  font-size: 1.5rem;
}

.result-card-label {
  font-size: 0.9rem;
  color: var(--neutral-600);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.result-card-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--neutral-800);
  margin-bottom: 0.5rem;
  font-family: 'Inter', sans-serif;
}

.result-card.highlight .result-card-value {
  color: var(--primary-color);
  font-size: 2.2rem;
}

.result-card-description {
  font-size: 0.8rem;
  color: var(--neutral-500);
  line-height: 1.4;
}

/* Legacy Result Rows (for backward compatibility) */
.result-row {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--neutral-200);
  flex-wrap: wrap;
  /* Allow wrapping on very small screens */
}

.result-row>span:first-child {
  margin-right: 1rem;
  font-weight: 500;
}

.result-row>span:last-child {
  font-weight: 600;
  text-align: right;
}

.result-row.highlight {
  font-weight: 700;
  font-size: var(--text-lg);
  color: var(--primary-dark);
  background-color: rgba(67, 97, 238, 0.05);
  padding: 0.75rem;
  border-radius: 0.25rem;
  margin: 0.5rem 0;
}

/* Modern Progress Ring */
.progress-ring {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto 1rem;
}

.progress-ring svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.progress-ring-circle {
  fill: none;
  stroke-width: 8;
  stroke-linecap: round;
}

.progress-ring-background {
  stroke: var(--neutral-200);
}

.progress-ring-progress {
  stroke: url(#progressGradient);
  stroke-dasharray: 0 377;
  transition: stroke-dasharray 1.5s ease-in-out;
}

.progress-ring-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.progress-ring-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  display: block;
}

.progress-ring-label {
  font-size: 0.8rem;
  color: var(--neutral-600);
  display: block;
  margin-top: 0.25rem;
}

/* Animated Counter */
.animated-counter {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  font-family: 'Inter', sans-serif;
  transition: all 0.3s ease;
}

.animated-counter.counting {
  color: var(--secondary-color);
}

/* Modern Gauge Chart */
.gauge-container {
  position: relative;
  width: 200px;
  height: 120px;
  margin: 0 auto 1.5rem;
}

.gauge-svg {
  width: 100%;
  height: 100%;
}

.gauge-background {
  fill: none;
  stroke: var(--neutral-200);
  stroke-width: 12;
  stroke-linecap: round;
}

.gauge-progress {
  fill: none;
  stroke-width: 12;
  stroke-linecap: round;
  transition: stroke-dasharray 1.5s ease-in-out;
}

.gauge-needle {
  stroke: var(--neutral-800);
  stroke-width: 3;
  stroke-linecap: round;
  transition: transform 1.5s ease-in-out;
  transform-origin: 100px 100px;
}

.gauge-center {
  fill: var(--neutral-800);
}

.gauge-labels {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: var(--neutral-600);
  padding: 0 20px;
}

.gauge-value {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
}

.gauge-value-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  display: block;
}

.gauge-value-label {
  font-size: 0.9rem;
  color: var(--neutral-600);
  display: block;
}

/* Share button for results */
.share-results-btn {
  display: inline-block;
  margin-top: 1.5rem;
  padding: 0.75rem 1.25rem;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border: none;
  border-radius: 12px;
  font-size: var(--text-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
}

.share-results-btn:hover {
  background-color: var(--primary-dark, #3b5ce6);
  transform: translateY(-1px);
}

.share-results-btn:before {
  content: "📤";
  margin-right: 0.5rem;
}

/* Share Modal Styles */
.share-modal {
  display: none;
  position: fixed;
  z-index: 10000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  align-items: center;
  justify-content: center;
}

.share-modal-content {
  background-color: white;
  border-radius: 8px;
  padding: 0;
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.share-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
}

.share-modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: var(--neutral-900, #212529);
}

.share-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--neutral-600, #6c757d);
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.share-modal-close:hover {
  background-color: var(--neutral-100, #f8f9fa);
}

.share-modal-body {
  padding: 1.5rem;
}

.share-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border: 2px solid var(--neutral-200, #e9ecef);
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: var(--neutral-700, #495057);
  font-size: 0.875rem;
  font-weight: 500;
}

.share-option:hover {
  border-color: var(--primary-color, #4a6cf7);
  background-color: var(--primary-50, #f8f9ff);
  transform: translateY(-2px);
}

.share-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  display: block;
}

/* Chart Containers */
.chart-container {
  margin-top: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.9));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  overflow-x: auto;
  /* Allow horizontal scrolling if needed */
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 16px 16px 0 0;
}

.chart-title {
  text-align: center;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: 1.5rem;
  position: relative;
}

.chart-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background: var(--primary-color);
  border-radius: 1px;
}

/* Simple Bar Chart */
.simple-chart {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  gap: 2rem;
  height: 280px;
  padding: 1rem 0;
  min-width: 300px;
  margin: 0 auto;
  position: relative;
}

.chart-bar {
  width: 80px;
  border-radius: 12px 12px 0 0;
  position: relative;
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
  box-shadow: 0 4px 20px rgba(67, 97, 238, 0.3);
  transform-origin: bottom;
  animation: barGrowth 1.2s ease-out;
}

@keyframes barGrowth {
  0% {
    height: 0 !important;
    opacity: 0;
  }

  50% {
    opacity: 0.7;
  }

  100% {
    opacity: 1;
  }
}

.chart-bar:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 30px rgba(67, 97, 238, 0.4);
}

.chart-bar.invested {
  background: linear-gradient(180deg, #10b981, #059669);
  box-shadow: 0 4px 20px rgba(16, 185, 129, 0.3);
}

.chart-bar.returns {
  background: linear-gradient(180deg, #f59e0b, #d97706);
  box-shadow: 0 4px 20px rgba(245, 158, 11, 0.3);
}

.chart-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.2), transparent);
  border-radius: 12px 12px 0 0;
  pointer-events: none;
}

.bar-label {
  position: absolute;
  bottom: -50px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  font-size: var(--text-sm);
  color: var(--neutral-700);
  font-weight: 500;
  white-space: nowrap;
  background: rgba(255, 255, 255, 0.9);
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive adjustments for charts */
@media (min-width: 768px) {
  .simple-chart {
    height: 300px;
    /* Restore height for larger screens */
    gap: 2rem;
    /* Restore gap for larger screens */
  }

  .chart-bar {
    width: 80px;
    /* Restore width for larger screens */
  }
}

/* Modern Pie Chart */
.pie-chart {
  width: 220px;
  height: 220px;
  border-radius: 50%;
  margin: 0 auto 2rem;
  position: relative;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  animation: pieChartAppear 1s ease-out;
}

@keyframes pieChartAppear {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.pie-chart:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.pie-chart::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  z-index: -1;
  opacity: 0.3;
}

.pie-label {
  position: absolute;
  font-weight: 600;
  color: white;
  font-size: 0.9rem;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  z-index: 2;
}

.principal-label {
  top: 40%;
  left: 30%;
  transform: translate(-50%, -50%);
}

.interest-label {
  top: 60%;
  left: 70%;
  transform: translate(-50%, -50%);
}

/* Modern Legend */
.breakdown-legend {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
  width: 100%;
  max-width: 200px;
}

.legend-item:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateX(4px);
}

.color-box {
  width: 20px;
  height: 20px;
  border-radius: 6px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  position: relative;
}

.color-box::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  height: 40%;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px 4px 0 0;
}

.principal-color {
  background: linear-gradient(135deg, #10b981, #059669);
}

.interest-color {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.legend-text {
  font-weight: 500;
  color: var(--neutral-700);
  font-size: 0.9rem;
}

/* Responsive adjustments for pie chart */
@media (min-width: 768px) {
  .pie-chart {
    width: 240px;
    height: 240px;
  }

  .pie-label {
    font-size: 1rem;
  }

  .breakdown-legend {
    flex-direction: row;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
  }

  .result-cards-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
  }

  .result-card {
    padding: 2rem;
  }

  .result-card-icon {
    width: 56px;
    height: 56px;
    font-size: 1.75rem;
  }

  .result-card-value {
    font-size: 2rem;
  }

  .result-card.highlight .result-card-value {
    font-size: 2.5rem;
  }

  .progress-ring {
    width: 150px;
    height: 150px;
  }

  .gauge-container {
    width: 240px;
    height: 140px;
  }
}

/* Mobile optimizations */
@media (max-width: 767px) {
  .result-cards-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .result-card {
    padding: 1.25rem;
  }

  .result-card-icon {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }

  .result-card-value {
    font-size: 1.5rem;
  }

  .result-card.highlight .result-card-value {
    font-size: 1.8rem;
  }

  .chart-container {
    padding: 1.5rem;
  }

  .simple-chart {
    height: 220px;
    gap: 1.5rem;
  }

  .chart-bar {
    width: 60px;
  }

  .progress-ring {
    width: 100px;
    height: 100px;
  }

  .gauge-container {
    width: 180px;
    height: 100px;
  }
}

/* BMI Scale */
.bmi-scale {
  margin-top: 2rem;
  width: 100%;
  overflow-x: hidden;
}

.scale-container {
  position: relative;
  height: 30px;
  /* Slightly smaller for mobile */
  background: linear-gradient(to right,
      #3a86ff 0%,
      #3a86ff 16.67%,
      #38b000 16.67%,
      #38b000 50%,
      #ffaa00 50%,
      #ffaa00 75%,
      #d00000 75%,
      #d00000 100%);
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.scale-marker {
  position: absolute;
  top: -10px;
  width: 8px;
  /* Slightly narrower for mobile */
  height: 50px;
  /* Slightly shorter for mobile */
  background-color: var(--neutral-900);
  transform: translateX(-50%);
  transition: left 0.5s ease;
}

.scale-segments {
  display: flex;
  width: 100%;
  margin-top: 0.5rem;
}

.segment {
  flex: 1;
  text-align: center;
  font-size: var(--text-xs);
  padding: 0.25rem 0;
  word-wrap: break-word;
}

.segment.underweight {
  color: #3a86ff;
}

.segment.normal {
  color: #38b000;
}

.segment.overweight {
  color: #ffaa00;
}

.segment.obese {
  color: #d00000;
}

.scale-values {
  display: flex;
  justify-content: space-between;
  margin-top: 0.25rem;
  font-size: var(--text-xs);
  color: var(--neutral-600);
}

/* Responsive adjustments for BMI scale */
@media (min-width: 768px) {
  .scale-container {
    height: 40px;
    /* Restore height for larger screens */
  }

  .scale-marker {
    width: 10px;
    /* Restore width for larger screens */
    height: 60px;
    /* Restore height for larger screens */
  }
}

/* Error Messages */
.error-message {
  color: var(--error-color);
  font-size: var(--text-sm);
  margin-top: 0.25rem;
}

/* Visual Content Strategy Components */

/* Interactive Comparison Charts */
.comparison-chart-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 1rem;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.comparison-chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.comparison-chart-title {
  font-family: var(--font-heading);
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: 1.5rem;
  text-align: center;
}

.comparison-split-view {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.comparison-side {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.comparison-side:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.comparison-side-title {
  font-family: var(--font-heading);
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.comparison-side.sip .comparison-side-title {
  color: var(--investment-color);
}

.comparison-side.lump-sum .comparison-side-title {
  color: var(--primary-color);
}

.comparison-metrics {
  list-style: none;
  padding: 0;
  margin: 0;
}

.comparison-metrics li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--neutral-200);
}

.comparison-metrics li:last-child {
  border-bottom: none;
}

.metric-label {
  font-weight: 500;
  color: var(--neutral-700);
}

.metric-value {
  font-weight: 600;
  color: var(--neutral-800);
  font-family: var(--font-mono);
}

.comparison-insight {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-top: 1.5rem;
  border-left: 4px solid var(--info-color);
}

.comparison-insight-title {
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.comparison-insight-text {
  color: var(--neutral-700);
  line-height: 1.6;
}

/* Interactive Timeline Visualization */
.timeline-visualization {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.timeline-title {
  font-family: var(--font-heading);
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: 2rem;
  text-align: center;
}

.timeline-container {
  position: relative;
  padding: 2rem 0;
}

.timeline-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--investment-color));
  border-radius: 2px;
  transform: translateY(-50%);
}

.timeline-points {
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.timeline-point {
  background: white;
  border: 4px solid var(--primary-color);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.timeline-point:hover {
  transform: scale(1.2);
  border-color: var(--investment-color);
}

.timeline-point-label {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--neutral-800);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.timeline-point:hover .timeline-point-label {
  opacity: 1;
}

.timeline-point-value {
  position: absolute;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  font-weight: 600;
  color: var(--neutral-800);
  font-size: 0.875rem;
  white-space: nowrap;
}

/* Progress Ring Enhancements */
.progress-ring-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 2rem 0;
}

.progress-ring-title {
  font-family: var(--font-heading);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: 1rem;
}

.progress-ring-description {
  color: var(--neutral-600);
  text-align: center;
  margin-top: 1rem;
  max-width: 300px;
}

/* Interactive Slider Components */
.interactive-slider-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 1rem;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.slider-title {
  font-family: var(--font-heading);
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: 1.5rem;
  text-align: center;
}

.slider-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.slider-control {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.slider-label {
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.slider-value {
  font-family: var(--font-mono);
  color: var(--primary-color);
  font-weight: 600;
}

.custom-slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: var(--neutral-200);
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.custom-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease;
}

.custom-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
}

.custom-slider::-moz-range-thumb {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.slider-results {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.slider-result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--neutral-200);
}

.slider-result-item:last-child {
  border-bottom: none;
}

.slider-result-label {
  font-weight: 500;
  color: var(--neutral-700);
}

.slider-result-value {
  font-weight: 600;
  color: var(--neutral-800);
  font-family: var(--font-mono);
}

.slider-result-highlight {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin: 0.5rem 0;
}

.slider-result-highlight .slider-result-value {
  color: var(--investment-color);
  font-size: 1.1rem;
}

/* Micro-Infographics for Social Media */
.micro-infographic {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 1rem;
  padding: 2rem;
  margin: 2rem 0;
  color: white;
  position: relative;
  overflow: hidden;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.micro-infographic::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.micro-infographic-hook {
  font-family: var(--font-heading);
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  position: relative;
  z-index: 2;
}

.micro-infographic-content {
  font-size: 1.25rem;
  line-height: 1.4;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 2;
}

.micro-infographic-stat {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0.75rem;
  padding: 1rem 1.5rem;
  margin: 1rem 0;
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 2;
}

.micro-infographic-stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  font-family: var(--font-mono);
  display: block;
}

.micro-infographic-stat-label {
  font-size: 1rem;
  opacity: 0.9;
  margin-top: 0.5rem;
}

.micro-infographic.gst-theme {
  background: linear-gradient(135deg, #4cc9f0 0%, #3a86ff 100%);
}

.micro-infographic.investment-theme {
  background: linear-gradient(135deg, #38b000 0%, #2d8a00 100%);
}

.micro-infographic.loan-theme {
  background: linear-gradient(135deg, #ff9e00 0%, #e68900 100%);
}

/* Data Storytelling Components */
.financial-journey {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
}

.journey-title {
  font-family: var(--font-heading);
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: 2rem;
  text-align: center;
}

.journey-phases {
  display: grid;
  gap: 2rem;
}

.journey-phase {
  display: grid;
  grid-template-columns: 80px 1fr;
  gap: 1.5rem;
  padding: 1.5rem;
  border-radius: 0.75rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.journey-phase:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.journey-phase.heavy-interest {
  background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
  border-left: 4px solid #f44336;
}

.journey-phase.turning-point {
  background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
  border-left: 4px solid #ff9800;
}

.journey-phase.wealth-building {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
  border-left: 4px solid #4caf50;
}

.journey-phase-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  font-weight: 600;
}

.journey-phase.heavy-interest .journey-phase-icon {
  background: linear-gradient(135deg, #f44336, #d32f2f);
}

.journey-phase.turning-point .journey-phase-icon {
  background: linear-gradient(135deg, #ff9800, #f57c00);
}

.journey-phase.wealth-building .journey-phase-icon {
  background: linear-gradient(135deg, #4caf50, #388e3c);
}

.journey-phase-content {
  flex: 1;
}

.journey-phase-title {
  font-family: var(--font-heading);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: 0.5rem;
}

.journey-phase-description {
  color: var(--neutral-700);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.journey-phase-insight {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 0.5rem;
  padding: 1rem;
  font-weight: 500;
  color: var(--neutral-800);
  border-left: 3px solid currentColor;
}

.journey-phase.heavy-interest .journey-phase-insight {
  border-left-color: #f44336;
}

.journey-phase.turning-point .journey-phase-insight {
  border-left-color: #ff9800;
}

.journey-phase.wealth-building .journey-phase-insight {
  border-left-color: #4caf50;
}

/* Visual Chart Enhancements */
.enhanced-chart-container {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--neutral-200);
}

.chart-title {
  font-family: var(--font-heading);
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--neutral-800);
}

.chart-controls {
  display: flex;
  gap: 0.5rem;
}

.chart-control-btn {
  background: var(--neutral-100);
  border: 1px solid var(--neutral-300);
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--neutral-700);
  cursor: pointer;
  transition: all 0.3s ease;
}

.chart-control-btn:hover,
.chart-control-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1.5rem;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--neutral-700);
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

.legend-color.principal {
  background: linear-gradient(135deg, var(--principal-color), #3a7bd5);
}

.legend-color.interest {
  background: linear-gradient(135deg, var(--interest-color), #ff6b35);
}

.legend-color.returns {
  background: linear-gradient(135deg, var(--returns-color), #2d8a00);
}

/* Responsive Design for Visual Components */
@media (max-width: 768px) {
  .comparison-split-view {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .slider-controls {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .journey-phase {
    grid-template-columns: 60px 1fr;
    gap: 1rem;
    padding: 1rem;
  }

  .journey-phase-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .micro-infographic-hook {
    font-size: 1.5rem;
  }

  .micro-infographic-content {
    font-size: 1.1rem;
  }

  .micro-infographic-stat-value {
    font-size: 2rem;
  }

  .chart-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .chart-legend {
    gap: 1rem;
  }
}

/* Simple Chart Components (HTML/CSS based) */
.simple-bar-chart {
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  height: 250px;
  padding: 1rem;
  background: linear-gradient(to top, var(--neutral-100) 0%, transparent 100%);
  border-radius: 0.5rem;
}

.bar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 80px;
}

.bar {
  width: 100%;
  min-height: 20px;
  border-radius: 4px 4px 0 0;
  position: relative;
  transition: all 0.3s ease;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 0.5rem;
}

.bar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.bar-value {
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.bar-label {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--neutral-700);
  text-align: center;
}

.simple-pie-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.pie-container {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  background: conic-gradient(var(--primary-color) 0deg 120deg,
      var(--interest-color) 120deg 240deg,
      var(--returns-color) 240deg 360deg);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.pie-legend {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.pie-legend-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--neutral-700);
}

.pie-legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

/* Step-by-Step Process Infographics */
.process-infographic {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.process-title {
  font-family: var(--font-heading);
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: 2rem;
  text-align: center;
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  position: relative;
}

.process-step {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.process-step:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.process-step::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 4px 4px 0 0;
}

.process-step-number {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 auto 1.5rem;
  box-shadow: 0 4px 16px rgba(67, 97, 238, 0.3);
}

.process-step-title {
  font-family: var(--font-heading);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: 1rem;
}

.process-step-description {
  color: var(--neutral-700);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.process-step-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.process-step-action {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.process-step-action:hover {
  background: var(--primary-dark);
}

/* Quick Tip Graphics */
.quick-tip-graphic {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 1rem;
  padding: 2rem;
  margin: 2rem 0;
  color: white;
  position: relative;
  overflow: hidden;
}

.quick-tip-graphic::before {
  content: '💡';
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 2rem;
  opacity: 0.3;
}

.quick-tip-title {
  font-family: var(--font-heading);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.quick-tip-content {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.quick-tip-highlight {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0.5rem;
  padding: 1rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

/* Social Share Optimized Graphics */
.share-optimized-graphic {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  min-height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.share-graphic-logo {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-family: var(--font-heading);
  font-weight: 700;
  color: var(--primary-color);
  font-size: 1rem;
}

.share-graphic-title {
  font-family: var(--font-heading);
  font-size: 2rem;
  font-weight: 700;
  color: var(--neutral-800);
  margin-bottom: 1.5rem;
}

.share-graphic-stat {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  border-radius: 1rem;
  padding: 2rem;
  margin: 1rem 0;
  min-width: 300px;
}

.share-graphic-stat-value {
  font-size: 3rem;
  font-weight: 700;
  font-family: var(--font-mono);
  display: block;
  margin-bottom: 0.5rem;
}

.share-graphic-stat-label {
  font-size: 1.25rem;
  opacity: 0.9;
}

.share-graphic-footer {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  color: var(--neutral-600);
  font-size: 0.875rem;
}

/* Animation Classes */
.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.scale-in {
  animation: scaleIn 0.4s ease-out;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

.slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Comparison Table */
.comparison-table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  font-size: var(--text-sm);
}

.comparison-table th,
.comparison-table td {
  padding: 0.75rem;
  text-align: left;
  border: 1px solid var(--neutral-300);
}

.comparison-table th {
  background-color: var(--neutral-100);
  font-weight: 600;
  color: var(--neutral-800);
}

.comparison-table tr:nth-child(even) {
  background-color: var(--neutral-50);
}

.comparison-table tr:hover {
  background-color: rgba(67, 97, 238, 0.05);
}

/* Tip Box */
.tip-box {
  background-color: rgba(56, 176, 0, 0.1);
  border-left: 4px solid var(--investment-color);
  padding: 1rem;
  margin: 1.5rem 0;
  border-radius: 0.25rem;
}

.tip-box h4 {
  color: var(--investment-color);
  margin-bottom: 0.5rem;
  font-size: var(--text-base);
}

.tip-box p {
  margin-bottom: 0;
  font-size: var(--text-sm);
}

/* Page Content Styles */
.main-content {
  min-height: 60vh;
  padding: 2rem 0;
}

.content-wrapper {
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 2px solid var(--neutral-200);
}

.page-header h1 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-size: 2.5rem;
}

.page-subtitle {
  font-size: 1.2rem;
  color: var(--neutral-600);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.last-updated {
  font-size: 0.9rem;
  color: var(--neutral-500);
  font-style: italic;
}

.content-body {
  line-height: 1.7;
}

/* Privacy Policy & How It Works Sections */
.privacy-section,
.how-it-works-section {
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid var(--neutral-200);
}

.privacy-section:last-child,
.how-it-works-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.privacy-section h2,
.how-it-works-section h2 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
}

.privacy-section h3,
.how-it-works-section h3 {
  color: var(--neutral-800);
  margin-bottom: 1rem;
  margin-top: 1.5rem;
  font-size: 1.3rem;
}

.highlight-box {
  background-color: rgba(67, 97, 238, 0.05);
  border-left: 4px solid var(--primary-color);
  padding: 1.5rem;
  margin: 2rem 0;
  border-radius: 0.25rem;
}

.highlight-box h3 {
  color: var(--primary-color);
  margin-top: 0;
  margin-bottom: 1rem;
}

.highlight-box ul {
  margin-bottom: 0;
}

/* Formula Categories */
.formula-category {
  margin-bottom: 2.5rem;
  padding: 1.5rem;
  background-color: var(--neutral-50);
  border-radius: 0.5rem;
  border: 1px solid var(--neutral-200);
}

.formula-category h3 {
  color: var(--primary-color);
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.4rem;
}

.formula-category ul {
  margin-bottom: 0;
}

.formula-category li {
  margin-bottom: 0.75rem;
}

.formula-category li:last-child {
  margin-bottom: 0;
}

/* Steps Grid */
.steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin: 2rem 0;
}

.step-item {
  background-color: white;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  border: 1px solid var(--neutral-200);
}

.step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
}

.step-item h3 {
  color: var(--neutral-800);
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.step-item p {
  color: var(--neutral-600);
  line-height: 1.6;
  margin-bottom: 0;
}

/* FAQ Styles */
.faq-category {
  margin-bottom: 3rem;
}

.faq-category h2 {
  color: var(--primary-color);
  margin-bottom: 2rem;
  font-size: 1.8rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-color);
}

.faq-item {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--neutral-200);
}

.faq-item h3 {
  color: var(--neutral-800);
  margin-bottom: 1rem;
  font-size: 1.2rem;
  line-height: 1.4;
}

.faq-item p {
  color: var(--neutral-600);
  line-height: 1.7;
  margin-bottom: 0;
}

.faq-item a {
  color: var(--primary-color);
  text-decoration: underline;
}

.faq-item a:hover {
  color: var(--primary-dark);
}

/* Mobile Responsive Styles for New Pages */
@media (max-width: 767px) {
  .main-content {
    padding: 1rem 0;
  }

  .page-header {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .page-subtitle {
    font-size: 1.1rem;
  }

  .privacy-section,
  .how-it-works-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
  }

  .privacy-section h2,
  .how-it-works-section h2 {
    font-size: 1.5rem;
  }

  .privacy-section h3,
  .how-it-works-section h3 {
    font-size: 1.2rem;
  }

  .highlight-box {
    padding: 1rem;
    margin: 1.5rem 0;
  }

  .formula-category {
    padding: 1rem;
    margin-bottom: 2rem;
  }

  .formula-category h3 {
    font-size: 1.2rem;
  }

  .steps-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin: 1.5rem 0;
  }

  .step-item {
    padding: 1.5rem;
  }

  .step-number {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }

  .step-item h3 {
    font-size: 1.1rem;
  }

  .faq-category {
    margin-bottom: 2rem;
  }

  .faq-category h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .faq-item {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .faq-item h3 {
    font-size: 1.1rem;
  }
}

/* Calculator Cards for Homepage */
.calculator-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 1.25rem;
  /* Slightly smaller padding for mobile */
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 1.5rem;
  /* Add bottom margin for mobile */
}

.calculator-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* Disable hover effect on touch devices */
@media (hover: none) {
  .calculator-card:hover {
    transform: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
}

.card-icon {
  width: 48px;
  /* Smaller for mobile */
  height: 48px;
  /* Smaller for mobile */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.card-title {
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
  /* Slightly smaller for mobile */
}

.card-description {
  color: var(--neutral-600);
  margin-bottom: 1.25rem;
  font-size: 0.9rem;
  /* Slightly smaller for mobile */
}

.card-link {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border-radius: 0.25rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
  -webkit-tap-highlight-color: transparent;
  /* Remove tap highlight on mobile */
}

.card-link:hover {
  background-color: var(--primary-dark);
  text-decoration: none;
}

/* Active state for better mobile feedback */
.card-link:active {
  transform: translateY(1px);
  background-color: var(--primary-dark);
}

/* Responsive adjustments for calculator cards */
@media (min-width: 768px) {
  .calculator-card {
    padding: 1.5rem;
    /* Restore padding for larger screens */
    margin-bottom: 0;
    /* Remove bottom margin for grid layout */
  }

  .card-icon {
    width: 64px;
    /* Restore size for larger screens */
    height: 64px;
    /* Restore size for larger screens */
  }

  .card-title {
    font-size: 1.5rem;
    /* Restore size for larger screens */
  }

  .card-description {
    font-size: 1rem;
    /* Restore size for larger screens */
    margin-bottom: 1.5rem;
    /* Restore margin for larger screens */
  }
}

/* Contact Form Styles */
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--neutral-300);
  border-radius: 0.25rem;
  font-size: var(--text-base);
  transition: border-color 0.2s ease;
  resize: vertical;
  min-height: 120px;
  /* Slightly smaller for mobile */
  box-sizing: border-box;
  -webkit-appearance: none;
  /* Removes default styling on iOS */
  appearance: none;
}

.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

.form-group.has-error input,
.form-group.has-error textarea,
.form-group.has-error select {
  border-color: var(--error-color);
}

.contact-info {
  margin-top: 2rem;
  padding: 1rem;
  background-color: var(--neutral-100);
  border-radius: 0.5rem;
}

.contact-item {
  margin-bottom: 1.5rem;
}

.contact-item h3 {
  margin-bottom: 0.5rem;
  color: var(--primary-color);
  font-size: 1.25rem;
  /* Slightly smaller for mobile */
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 1.25rem;
  /* Slightly smaller padding for mobile */
  border-radius: 0.25rem;
  margin-top: 1rem;
  text-align: center;
  width: 100%;
  box-sizing: border-box;
}

.success-message h3 {
  color: #155724;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  /* Slightly smaller for mobile */
}

.primary-btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-size: var(--text-base);
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 1rem;
  -webkit-tap-highlight-color: transparent;
  /* Remove tap highlight on mobile */
  width: 100%;
  /* Full width on mobile */
  text-align: center;
  box-sizing: border-box;
}

.primary-btn:hover {
  background-color: var(--primary-dark);
  text-decoration: none;
  color: white;
}

/* Active state for better mobile feedback */
.primary-btn:active {
  transform: translateY(1px);
  background-color: var(--primary-dark);
}

/* Related Calculators Section */
.related-calculators {
  background-color: var(--neutral-50);
  padding: 2rem 0;
  margin-top: 3rem;
}

.related-calculators .section-title {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--neutral-800);
  font-size: 1.75rem;
  font-weight: 600;
}

.calculator-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.calculator-grid .calculator-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid var(--neutral-200);
  margin-bottom: 0;
}

.calculator-grid .calculator-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.calculator-grid .calculator-card h3 {
  margin-bottom: 0.75rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary-color);
}

.calculator-grid .calculator-card h3 a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

.calculator-grid .calculator-card h3 a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.calculator-grid .calculator-card p {
  color: var(--neutral-600);
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
}

/* Responsive adjustments for related calculators */
@media (max-width: 768px) {
  .related-calculators {
    padding: 1.5rem 0;
    margin-top: 2rem;
  }

  .related-calculators .section-title {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .calculator-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0 0.75rem;
  }

  .calculator-grid .calculator-card {
    padding: 1.25rem;
  }

  .calculator-grid .calculator-card h3 {
    font-size: 1.1rem;
  }
}

/* Disable hover effect on touch devices */
@media (hover: none) {
  .calculator-grid .calculator-card:hover {
    transform: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
}

/* Blog Styles */
.blog-header {
  text-align: center;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid var(--neutral-200);
}

.blog-header h1 {
  color: var(--neutral-900);
  margin-bottom: 1rem;
}

.blog-subtitle {
  font-size: 1.2rem;
  color: var(--neutral-600);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.featured-articles,
.latest-articles,
.blog-categories,
.category-section,
.all-articles {
  margin-bottom: 4rem;
}

.featured-articles h2,
.latest-articles h2,
.blog-categories h2,
.category-section h2,
.all-articles h2 {
  color: var(--neutral-900);
  margin-bottom: 2rem;
  font-size: 2rem;
  text-align: center;
}

.blog-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.blog-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.blog-card.featured {
  border: 2px solid var(--primary-color);
}

.blog-card-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.blog-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.blog-card:hover .blog-card-image img {
  transform: scale(1.05);
}

.blog-card-content {
  padding: 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.blog-category {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background-color: var(--primary-color);
  color: white;
  font-size: 0.8rem;
  font-weight: 500;
  border-radius: 1rem;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.blog-card h3 {
  margin-bottom: 1rem;
  font-size: 1.25rem;
  line-height: 1.4;
}

.blog-card h3 a {
  color: var(--neutral-900);
  text-decoration: none;
  transition: color 0.2s ease;
}

.blog-card h3 a:hover {
  color: var(--primary-color);
  text-decoration: none;
}

.blog-card p {
  color: var(--neutral-600);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

.blog-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.9rem;
  color: var(--neutral-500);
  margin-top: auto;
}

.blog-date,
.blog-read-time {
  display: flex;
  align-items: center;
}

.category-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.category-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.category-card h3 {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.category-card h3 a {
  color: var(--neutral-900);
  text-decoration: none;
  transition: color 0.2s ease;
}

.category-card h3 a:hover {
  color: var(--primary-color);
  text-decoration: none;
}

.category-card p {
  color: var(--neutral-600);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

.article-count {
  font-size: 0.9rem;
  color: var(--neutral-500);
  font-weight: 500;
  margin-top: auto;
}

.category-section {
  padding: 2rem 0;
  border-bottom: 1px solid var(--neutral-200);
}

.category-section:last-of-type {
  border-bottom: none;
}

/* FAQ and content sections */
.faq-item {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--neutral-200);
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-item h3 {
  margin-bottom: 0.75rem;
  color: var(--neutral-800);
  font-size: 1.1rem;
  /* Smaller for mobile */
}

/* Responsive adjustments for contact form and content */
@media (min-width: 768px) {
  .form-group textarea {
    min-height: 150px;
    /* Restore height for larger screens */
  }

  .contact-item h3 {
    font-size: 1.5rem;
    /* Restore size for larger screens */
  }

  .success-message {
    padding: 1.5rem;
    /* Restore padding for larger screens */
  }

  .success-message h3 {
    font-size: 1.5rem;
    /* Restore size for larger screens */
  }

  .primary-btn {
    width: auto;
    /* Auto width on larger screens */
  }

  .faq-item h3 {
    font-size: 1.25rem;
    /* Restore size for larger screens */
  }

  /* Blog responsive styles for larger screens */
  .blog-header {
    margin-bottom: 4rem;
    padding-bottom: 3rem;
  }

  .blog-subtitle {
    font-size: 1.3rem;
  }

  .category-icon {
    width: 80px;
    height: 80px;
  }

  .blog-card h3 {
    font-size: 1.4rem;
  }

  .category-card h3 {
    font-size: 1.4rem;
  }
}

/* Category Header Styles */
.category-header {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  margin-bottom: 3rem;
  padding: 2rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--neutral-200);
}

.category-info {
  flex: 1;
}

.category-description {
  color: var(--neutral-600);
  font-size: var(--text-lg);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.category-extended-description {
  color: var(--neutral-600);
  font-size: var(--text-base);
  line-height: 1.7;
  margin-bottom: 0;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--neutral-200);
}

/* Responsive adjustments for category header */
@media (max-width: 767px) {
  .category-header {
    flex-direction: column;
    gap: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .category-description {
    font-size: var(--text-base);
  }

  .category-extended-description {
    font-size: var(--text-sm);
    margin-top: 0.75rem;
    padding-top: 0.75rem;
  }
}

/* Blog mobile responsive styles */
@media (max-width: 767px) {
  .blog-header {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
  }

  .blog-header h1 {
    font-size: 2rem;
  }

  .blog-subtitle {
    font-size: 1rem;
  }

  .featured-articles,
  .latest-articles,
  .blog-categories,
  .category-section,
  .all-articles {
    margin-bottom: 2.5rem;
  }

  .featured-articles h2,
  .latest-articles h2,
  .blog-categories h2,
  .category-section h2,
  .all-articles h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .blog-card-content {
    padding: 1rem;
  }

  .blog-card h3 {
    font-size: 1.1rem;
  }

  .category-card {
    padding: 1.5rem;
  }

  .category-icon {
    width: 48px;
    height: 48px;
    margin-bottom: 1rem;
  }

  .category-card h3 {
    font-size: 1.1rem;
  }

  .category-section {
    padding: 1.5rem 0;
  }

  .blog-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Advanced Chart.js Integration Styles */
.advanced-chart-wrapper {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  height: 400px;
}

.advanced-chart-title {
  font-family: var(--font-heading);
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: 1.5rem;
  text-align: center;
}

.advanced-chart-wrapper canvas {
  max-height: 300px !important;
}

/* Investment Comparison Dashboard */
.investment-dashboard {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 2rem 0;
}

.dashboard-card {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.dashboard-card-title {
  font-family: var(--font-heading);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dashboard-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--neutral-200);
}

.dashboard-metric:last-child {
  border-bottom: none;
}

.dashboard-metric-label {
  font-weight: 500;
  color: var(--neutral-700);
}

.dashboard-metric-value {
  font-weight: 600;
  color: var(--neutral-800);
  font-family: var(--font-mono);
}

/* Interactive Chart Controls */
.chart-controls-advanced {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.chart-control-advanced {
  background: var(--neutral-100);
  border: 2px solid var(--neutral-300);
  border-radius: 0.75rem;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--neutral-700);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.chart-control-advanced:hover,
.chart-control-advanced.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
}

/* Responsive Design for Advanced Charts */
@media (max-width: 768px) {
  .investment-dashboard {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .advanced-chart-wrapper {
    padding: 1.5rem;
    height: 350px;
  }

  .advanced-chart-wrapper canvas {
    max-height: 250px !important;
  }

  .chart-controls-advanced {
    gap: 0.5rem;
  }

  .chart-control-advanced {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
}