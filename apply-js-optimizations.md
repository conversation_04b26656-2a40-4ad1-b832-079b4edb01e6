# JavaScript Optimization Application Guide

## Quick Reference for Applying JS Optimizations

### Step 1: Replace Google Analytics in `<head>`

**Find this pattern:**
```html
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-6BNPSB8DSK"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag() { dataLayer.push(arguments); }
  gtag('js', new Date());

  gtag('config', 'G-6BNPSB8DSK');
</script>
```

**Replace with:**
```html
<!-- Deferred Google Analytics - Load after user interaction -->
<script>
  // Minimal analytics setup - defer full loading
  window.dataLayer = window.dataLayer || [];
  function gtag() { dataLayer.push(arguments); }
  
  // Track initial page view without loading full GTM
  gtag('js', new Date());
  gtag('config', 'G-6BNPSB8DSK', {
    'send_page_view': false // Prevent automatic page view
  });
  
  // Load Google Analytics after user interaction or 3 seconds
  let analyticsLoaded = false;
  
  function loadAnalytics() {
    if (analyticsLoaded) return;
    analyticsLoaded = true;
    
    const script = document.createElement('script');
    script.async = true;
    script.src = 'https://www.googletagmanager.com/gtag/js?id=G-6BNPSB8DSK';
    document.head.appendChild(script);
    
    script.onload = function() {
      // Send the page view after analytics loads
      gtag('config', 'G-6BNPSB8DSK', {
        'page_title': document.title,
        'page_location': window.location.href
      });
    };
  }
  
  // Load analytics on first user interaction
  ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'].forEach(function(event) {
    document.addEventListener(event, loadAnalytics, { once: true, passive: true });
  });
  
  // Fallback: load after 3 seconds if no interaction
  setTimeout(loadAnalytics, 3000);
</script>
```

### Step 2: Replace Script Loading at Bottom

**For Calculator Pages - Find this pattern:**
```html
<!-- Scripts -->
<script src="../assets/js/utils.js" defer></script>
<script src="../assets/js/main.js" defer></script>
<script src="../assets/js/calculators/[calculator-type].js" defer></script>
```

**Replace with (adjust paths and calculator type):**
```html
<!-- Optimized Script Loading -->
<script>
  // Load scripts conditionally and efficiently
  function loadScript(src, callback) {
    const script = document.createElement('script');
    script.src = src;
    script.defer = true;
    if (callback) script.onload = callback;
    document.head.appendChild(script);
  }
  
  // Load essential utils first
  loadScript('../assets/js/utils.js', function() {
    // Load main.js after utils.js is ready
    loadScript('../assets/js/main.js', function() {
      // Load calculator-specific script only when calculator is used
      const calculatorForm = document.getElementById('[calculator-form-id]');
      if (calculatorForm) {
        // Load calculator script when user interacts with form
        const loadCalculatorScript = function() {
          loadScript('../assets/js/calculators/[calculator-type].js');
          calculatorForm.removeEventListener('focusin', loadCalculatorScript);
          calculatorForm.removeEventListener('click', loadCalculatorScript);
        };
        
        calculatorForm.addEventListener('focusin', loadCalculatorScript, { once: true });
        calculatorForm.addEventListener('click', loadCalculatorScript, { once: true });
        
        // Fallback: load after 2 seconds if no interaction
        setTimeout(loadCalculatorScript, 2000);
      }
    });
  });
</script>
```

**For Non-Calculator Pages (Homepage, Blog, etc.):**
```html
<!-- Optimized Script Loading -->
<script>
  // Load scripts conditionally and efficiently
  function loadScript(src, callback) {
    const script = document.createElement('script');
    script.src = src;
    script.defer = true;
    if (callback) script.onload = callback;
    document.head.appendChild(script);
  }
  
  // Load essential utils first
  loadScript('assets/js/utils.js', function() {
    // Load main.js after utils.js is ready
    loadScript('assets/js/main.js');
  });
</script>
```

## Calculator Form IDs Reference

### Tax Calculators:
- GST Calculator: `gst-calculator-form`
- Income Tax: `income-tax-form`
- Tax Comparison: `tax-comparison-form`

### Investment Calculators:
- SIP Calculator: `sip-calculator-form`
- Compound Interest: `compound-interest-form`
- Lump Sum: `lump-sum-form`
- Goal Calculator: `goal-calculator-form`

### Loan Calculators:
- EMI Calculator: `emi-calculator-form`
- Mortgage: `mortgage-calculator-form`
- Affordability: `affordability-form`
- Comparison: `loan-comparison-form`

### Health Calculators:
- BMI Calculator: `bmi-calculator-form`
- Calorie Calculator: `calorie-calculator-form`
- Pregnancy: `pregnancy-calculator-form`
- Body Fat: `body-fat-form`

### Discount Calculators:
- Percentage: `percentage-discount-form`
- Amount-based: `amount-discount-form`
- Bulk Discount: `bulk-discount-form`

## Calculator Script Types Reference

### Script Mapping:
- Tax calculators → `tax.js`
- Investment calculators → `investment.js`
- Loan calculators → `mortgage.js`
- Health calculators → `health.js`
- Discount calculators → `discount.js`

## Path Adjustments

### For Root Level Files:
- Use `assets/js/` (no ../)

### For Subdirectory Files:
- Use `../assets/js/`

## Files Requiring Optimization

### Priority 1 (High Traffic):
- [ ] loan/emi-calculator.html
- [ ] health/bmi-calculator.html
- [ ] discount/percentage.html
- [ ] tax/income-tax.html

### Priority 2 (Medium Traffic):
- [ ] loan/mortgage-calculator.html
- [ ] health/calorie-calculator.html
- [ ] investment/compound-interest.html
- [ ] tax/tax-comparison.html

### Priority 3 (Static Pages):
- [ ] blog/index.html
- [ ] how-it-works.html
- [ ] faq.html
- [ ] privacy.html
- [ ] contact.html

## Validation Checklist

After applying optimizations:

1. **Analytics Check**: Verify Google Analytics still tracks page views
2. **Calculator Check**: Test calculator functionality works correctly
3. **Console Check**: No JavaScript errors in browser console
4. **Performance Check**: Run Lighthouse audit for improvements
5. **Interaction Check**: Verify scripts load on form interaction

## Expected Results

- **JavaScript reduction**: 52.2 KiB less unused code initially
- **FCP improvement**: 500ms-1s faster First Contentful Paint
- **TTI improvement**: 1-2s faster Time to Interactive
- **Performance score**: +15-25 points in Lighthouse
- **User experience**: Immediate page responsiveness

## Troubleshooting

### If Analytics Not Working:
1. Check browser console for errors
2. Verify GTM ID is correct
3. Test interaction triggers manually
4. Check fallback timer (3 seconds)

### If Calculator Not Working:
1. Verify form ID matches script
2. Check script path is correct
3. Ensure utils.js loads before calculator script
4. Test interaction triggers on form elements
