/**
 * Mobile Optimizations
 * Specific optimizations for mobile devices
 */

/* Accessibility Improvements for Touch Targets */
@media (max-width: 768px) {

  /* Buttons with improved touch targets */
  .primary-btn,
  .secondary-btn,
  button[type="submit"],
  .card-link,
  .calculate-btn,
  .reset-btn,
  .share-results-btn {
    min-height: 44px;
    min-width: 44px;
  }

  /* Links with improved touch targets - simplified to avoid conflicts */
  .nav-link,
  .dropdown-menu a,
  .footer-links a,
  .sidebar-section a {
    min-height: 44px;
  }

  /* Form elements with improved accessibility */
  input[type="text"],
  input[type="number"],
  input[type="email"],
  select,
  textarea {
    font-size: 16px;
    /* Prevent iOS zoom on focus */
  }

  /* Mobile-specific share modal styles */
  .share-modal-content {
    width: 95%;
    max-width: 350px;
    margin: 1rem;
  }

  .share-options {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .share-option {
    padding: 0.75rem 0.5rem;
    font-size: 0.8rem;
  }

  .share-icon {
    font-size: 1.25rem;
    margin-bottom: 0.25rem;
  }

  .share-modal-header {
    padding: 1rem;
  }

  .share-modal-body {
    padding: 1rem;
  }

  /* Fix for iOS input zoom */
  @media screen and (-webkit-min-device-pixel-ratio: 0) {

    select,
    textarea,
    input[type="text"],
    input[type="number"],
    input[type="email"] {
      font-size: 16px;
    }
  }

  /* Improve table responsiveness */
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Ensure minimum touch target size for checkboxes and radio buttons */
  input[type="checkbox"],
  input[type="radio"] {
    min-width: 24px;
    min-height: 24px;
  }
}

/* Performance Optimizations */
/* Add loading attribute to images */
img[loading="lazy"] {
  transition: opacity 0.3s;
}

img[loading="lazy"]:not([src]) {
  opacity: 0;
}

/* Add support for prefers-reduced-motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Add support for dark mode - commented out to avoid conflicts */
/*
@media (prefers-color-scheme: dark) {
  .calculator-container,
  .calculator-card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
  }
}
*/