<svg width="400" height="250" viewBox="0 0 400 250" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="healthGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fa709a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fee140;stop-opacity:1" />
    </linearGradient>
    <style>
      .title-text { font-family: 'Poppins', sans-serif; font-weight: 700; font-size: 20px; fill: white; }
      .category-text { font-family: 'Poppins', sans-serif; font-weight: 500; font-size: 12px; fill: white; opacity: 0.9; text-transform: uppercase; letter-spacing: 1px; }
      .brand-text { font-family: 'Poppins', sans-serif; font-weight: 500; font-size: 14px; fill: white; opacity: 0.8; }
    </style>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="250" rx="12" fill="url(#healthGradient)"/>
  
  <!-- Decorative circles -->
  <circle cx="320" cy="50" r="2" fill="white" opacity="0.3"/>
  <circle cx="360" cy="80" r="1.5" fill="white" opacity="0.2"/>
  <circle cx="340" cy="120" r="1" fill="white" opacity="0.4"/>
  
  <!-- Health/Heart icon -->
  <g transform="translate(340, 20)" fill="white" opacity="0.3">
    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
  </g>
  
  <!-- Content -->
  <text x="30" y="50" class="category-text">Health &amp; Wellness</text>
  <text x="30" y="80" class="title-text">Understanding BMI</text>
  <text x="30" y="105" class="title-text">&amp; Health Assessment</text>
  
  <!-- Brand -->
  <text x="270" y="220" class="brand-text">CalculatorSuites</text>
</svg>
