/* Main CSS File for Calculator Suites */

/* CSS Variables */
:root {
  /* Primary Colors */
  --primary-color: #4361ee;
  --primary-light: #4895ef;
  --primary-dark: #3a0ca3;

  /* Secondary Colors */
  --secondary-color: #f72585;
  --secondary-light: #ff758f;
  --secondary-dark: #b5179e;

  /* Neutral Colors */
  --neutral-100: #f8f9fa;
  --neutral-200: #e9ecef;
  --neutral-300: #dee2e6;
  --neutral-400: #ced4da;
  --neutral-500: #adb5bd;
  --neutral-600: #6c757d;
  --neutral-700: #495057;
  --neutral-800: #343a40;
  --neutral-900: #212529;

  /* Semantic Colors */
  --success-color: #38b000;
  --warning-color: #ffaa00;
  --error-color: #d00000;
  --info-color: #3a86ff;

  /* Calculator-specific Colors */
  --tax-color: #4cc9f0;
  --discount-color: #f72585;
  --investment-color: #38b000;
  --mortgage-color: #ff9e00;
  --health-color: #4361ee;

  /* Chart Colors */
  --principal-color: #4361ee;
  --interest-color: #ff9e00;
  --returns-color: #38b000;
  --invested-color: #3a86ff;

  /* Font Families */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-mono: 'JetBrains Mono', 'SF Mono', 'Fira Code', Consolas, 'Courier New', monospace;

  /* Font Sizes */
  --text-xs: 0.75rem;
  /* 12px */
  --text-sm: 0.875rem;
  /* 14px */
  --text-base: 1rem;
  /* 16px */
  --text-lg: 1.125rem;
  /* 18px */
  --text-xl: 1.25rem;
  /* 20px */
  --text-2xl: 1.5rem;
  /* 24px */
  --text-3xl: 1.875rem;
  /* 30px */
  --text-4xl: 2.25rem;
  /* 36px */

  /* Line Heights */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;

  /* Breakpoints */
  --breakpoint-sm: 640px;
  /* Small devices (phones) */
  --breakpoint-md: 768px;
  /* Medium devices (tablets) */
  --breakpoint-lg: 1024px;
  /* Large devices (desktops) */
  --breakpoint-xl: 1280px;
  /* Extra large devices */
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Global list style reset - prevent any unwanted numbering */
nav ul,
nav ol,
nav li,
.nav-menu,
.nav-menu li,
.nav-item,
.dropdown-menu,
.dropdown-menu li,
.breadcrumb,
.breadcrumb li,
.breadcrumb-item {
  list-style: none !important;
  list-style-type: none !important;
}

/* Prevent any pseudo-element numbering on navigation */
nav li::before,
.nav-menu li::before,
.nav-item::before,
.dropdown-menu li::before,
.breadcrumb li::before,
.breadcrumb-item::before {
  content: none !important;
}

/* Force override any browser default numbering */
nav ol,
.breadcrumb {
  counter-reset: none !important;
}

nav ol li,
.breadcrumb li,
.breadcrumb-item {
  counter-increment: none !important;
}

nav ol li::marker,
.breadcrumb li::marker,
.breadcrumb-item::marker {
  content: none !important;
  display: none !important;
}

/* Additional safety: Remove any possible numbering from header navigation */
header nav ul,
header nav ol,
header nav li,
.site-header nav ul,
.site-header nav ol,
.site-header nav li {
  list-style: none !important;
  list-style-type: none !important;
}

header nav li::before,
.site-header nav li::before {
  content: none !important;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--neutral-800);
  background-color: var(--neutral-100);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-heading);
  font-weight: 600;
  line-height: var(--leading-tight);
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--neutral-900);
}

h1 {
  font-size: var(--text-4xl);
}

h2 {
  font-size: var(--text-3xl);
}

h3 {
  font-size: var(--text-2xl);
}

h4 {
  font-size: var(--text-xl);
}

p {
  margin-bottom: 1rem;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

img {
  max-width: 100%;
  height: auto;
}

/* Layout */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  box-sizing: border-box;
}

.grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 1.5rem;
  width: 100%;
}

.section {
  padding: 3rem 0;
  width: 100%;
}

/* Header and Navigation */
.site-header {
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  width: 100%;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  width: 100%;
}

.logo {
  display: flex;
  align-items: center;
  z-index: 101;
  /* Ensure logo stays above mobile menu */
}

.logo-text {
  font-family: var(--font-heading);
  font-size: 1.5rem;
  /* Slightly smaller for better mobile display */
  font-weight: 700;
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
}

.nav-menu {
  display: none;
  list-style: none;
  list-style-type: none;
  margin: 0;
  padding: 0;
}

/* Ensure no numbering on any navigation elements */
.nav-menu li,
.nav-item,
.dropdown-menu li {
  list-style: none !important;
  list-style-type: none !important;
}

.nav-menu li::before,
.nav-item::before,
.dropdown-menu li::before {
  content: none !important;
}

.nav-item {
  position: relative;
  margin-left: 1.5rem;
}

.nav-link {
  color: var(--neutral-800);
  font-weight: 500;
  padding: 0.5rem 0;
  transition: color 0.2s ease;
}

.nav-link:hover {
  color: var(--primary-color);
  text-decoration: none;
}

.dropdown-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background-color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 0.25rem;
  padding: 0.5rem 0;
  min-width: 200px;
  z-index: 10;
}

.dropdown-menu li {
  list-style: none;
  list-style-type: none;
}

.dropdown-menu a {
  display: block;
  padding: 0.5rem 1rem;
  color: var(--neutral-800);
  transition: all 0.2s ease;
}

.dropdown-menu a:hover {
  background-color: var(--neutral-100);
  color: var(--primary-color);
  text-decoration: none;
}

.has-dropdown:hover .dropdown-menu {
  display: block;
}

.mobile-menu-toggle {
  display: block;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  z-index: 101;
  /* Ensure toggle stays above mobile menu */
  margin-left: auto;
}

.hamburger-icon {
  display: block;
  width: 24px;
  height: 2px;
  background-color: var(--neutral-800);
  position: relative;
  transition: background-color 0.3s ease;
}

.hamburger-icon::before,
.hamburger-icon::after {
  content: '';
  display: block;
  width: 24px;
  height: 2px;
  background-color: var(--neutral-800);
  position: absolute;
  left: 0;
  transition: transform 0.3s ease;
}

.hamburger-icon::before {
  top: -6px;
}

.hamburger-icon::after {
  bottom: -6px;
}

/* Active state for mobile menu toggle */
.mobile-menu-toggle.active .hamburger-icon {
  background-color: transparent;
}

.mobile-menu-toggle.active .hamburger-icon::before {
  transform: rotate(45deg);
  top: 0;
}

.mobile-menu-toggle.active .hamburger-icon::after {
  transform: rotate(-45deg);
  bottom: 0;
}

/* Media query for navigation */
@media (min-width: 768px) {
  .mobile-menu-toggle {
    display: none;
  }

  .nav-menu {
    display: flex;
  }

  .logo-text {
    font-size: 1.75rem;
    /* Restore original size for larger screens */
  }
}

/* Category Navigation */
.category-navigation {
  background-color: white;
  border: 1px solid var(--neutral-200);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.category-navigation h2 {
  font-size: 1.5rem;
  color: var(--neutral-900);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-color);
}

.category-navigation>ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.category-navigation>ul>li {
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--neutral-200);
  padding-bottom: 0.75rem;
}

.category-navigation>ul>li:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.category-navigation>ul>li>a {
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--primary-color);
  text-decoration: none;
  display: block;
  padding: 0.5rem 0;
  transition: color 0.2s ease;
}

.category-navigation>ul>li>a:hover {
  color: var(--primary-dark);
  text-decoration: none;
}

.category-navigation ul ul {
  list-style: none;
  margin: 0.5rem 0 0 0;
  padding: 0;
}

.category-navigation ul ul li {
  margin-bottom: 0.5rem;
  padding-left: 1rem;
  position: relative;
}

.category-navigation ul ul li:before {
  content: "→";
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

.category-navigation ul ul li a {
  color: var(--neutral-700);
  text-decoration: none;
  font-size: 0.95rem;
  line-height: 1.4;
  transition: color 0.2s ease;
}

.category-navigation ul ul li a:hover {
  color: var(--primary-color);
  text-decoration: none;
}

/* Scroll to top button */
.scroll-to-top {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 1.5rem;
  line-height: 1;
  cursor: pointer;
  display: none;
  z-index: 99;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: background-color 0.2s ease;
  -webkit-tap-highlight-color: transparent;
}

.scroll-to-top:hover {
  background-color: var(--primary-dark);
}

/* Adjust scroll to top button for mobile */
@media (max-width: 767px) {
  .scroll-to-top {
    width: 36px;
    height: 36px;
    bottom: 15px;
    right: 15px;
    font-size: 1.25rem;
  }
}

/* Enhanced FAQ Styles */
.calculator-faq {
  margin-top: 3rem;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.calculator-faq h2 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
}

/* FAQ Search and Filter */
.faq-controls {
  margin-bottom: 2rem;
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: center;
}

.faq-search {
  flex: 1;
  min-width: 250px;
}

.faq-search input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  background: white;
}

.faq-search input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.faq-filters {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.faq-filter-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  background: white;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.faq-filter-btn:hover,
.faq-filter-btn.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

/* FAQ Categories */
.faq-category {
  margin-bottom: 2rem;
}

.faq-category h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.4rem;
  padding: 0.75rem 1rem;
  background: #ecf0f1;
  border-radius: 6px;
  border-left: 4px solid #3498db;
}

/* Enhanced FAQ Items */
.faq-item {
  margin-bottom: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.faq-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.faq-question {
  padding: 1.25rem 1.5rem;
  background: #f8f9fa;
  border: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
  font-size: 1.1rem;
  transition: background-color 0.3s ease;
}

.faq-question:hover {
  background: #e9ecef;
}

.faq-question h4 {
  margin: 0;
  flex: 1;
  font-size: 1.1rem;
  font-weight: 600;
}

.faq-toggle {
  font-size: 1.2rem;
  color: #3498db;
  transition: transform 0.3s ease;
}

.faq-item.active .faq-toggle {
  transform: rotate(180deg);
}

.faq-answer {
  padding: 0 1.5rem;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
  padding: 1.5rem;
  max-height: 1000px;
}

.faq-answer p {
  color: #555;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.faq-answer ul {
  margin-left: 1.5rem;
  color: #555;
  margin-bottom: 1rem;
}

.faq-answer li {
  margin-bottom: 0.5rem;
}

.faq-answer strong {
  color: #2c3e50;
}

.faq-answer em {
  color: #7f8c8d;
  font-style: italic;
}

/* FAQ Comparison Tables */
.sip-comparison,
.tenure-comparison,
.prepayment-scenarios,
.prepayment-comparison,
.eligibility-factors {
  margin: 1rem 0;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #3498db;
}

.sip-comparison h5,
.tenure-comparison h5,
.prepayment-scenarios h5,
.prepayment-comparison h5,
.eligibility-factors h5 {
  color: #2c3e50;
  margin-bottom: 0.75rem;
  font-size: 1rem;
}

/* No Results Message */
.faq-no-results {
  text-align: center;
  padding: 2rem;
  color: #7f8c8d;
  font-style: italic;
  display: none;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .faq-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .faq-search {
    min-width: auto;
  }

  .faq-filters {
    justify-content: center;
  }

  .faq-question {
    padding: 1rem;
    font-size: 1rem;
  }

  .faq-answer {
    padding: 0 1rem;
  }

  .faq-item.active .faq-answer {
    padding: 1rem;
  }
}