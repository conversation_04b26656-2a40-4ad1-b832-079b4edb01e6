<svg width="400" height="250" viewBox="0 0 400 250" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="businessGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a8edea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fed6e3;stop-opacity:1" />
    </linearGradient>
    <style>
      .title-text { font-family: 'Poppins', sans-serif; font-weight: 700; font-size: 20px; fill: #333; }
      .category-text { font-family: 'Poppins', sans-serif; font-weight: 500; font-size: 12px; fill: #333; opacity: 0.9; text-transform: uppercase; letter-spacing: 1px; }
      .brand-text { font-family: 'Poppins', sans-serif; font-weight: 500; font-size: 14px; fill: #333; opacity: 0.8; }
    </style>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="250" rx="12" fill="url(#businessGradient)"/>
  
  <!-- Decorative circles -->
  <circle cx="320" cy="50" r="2" fill="#333" opacity="0.3"/>
  <circle cx="360" cy="80" r="1.5" fill="#333" opacity="0.2"/>
  <circle cx="340" cy="120" r="1" fill="#333" opacity="0.4"/>
  
  <!-- Warning icon -->
  <g transform="translate(340, 20)" fill="#333" opacity="0.3">
    <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
  </g>
  
  <!-- Content -->
  <text x="30" y="50" class="category-text">Tax Planning</text>
  <text x="30" y="80" class="title-text">Common GST</text>
  <text x="30" y="105" class="title-text">Filing Mistakes</text>
  
  <!-- Brand -->
  <text x="270" y="220" class="brand-text">CalculatorSuites</text>
</svg>
