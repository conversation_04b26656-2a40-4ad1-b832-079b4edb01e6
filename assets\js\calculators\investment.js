/**
 * Investment Calculator Scripts
 */

document.addEventListener("DOMContentLoaded", function () {
  // Initialize SIP Calculator if it exists on the page
  const sipCalculatorForm = document.getElementById("sip-calculator-form");
  if (sipCalculatorForm) {
    initSIPCalculator();
  }

  // Initialize Compound Interest Calculator if it exists on the page
  const compoundInterestForm = document.getElementById(
    "compound-interest-form",
  );
  if (compoundInterestForm) {
    initCompoundInterestCalculator();
  }

  // Initialize Lump Sum Calculator if it exists on the page
  const lumpSumForm = document.getElementById("lump-sum-form");
  if (lumpSumForm) {
    initLumpSumCalculator();
  }

  // Initialize Investment Goal Calculator if it exists on the page
  const goalCalculatorForm = document.getElementById("goal-calculator-form");
  if (goalCalculatorForm) {
    initGoalCalculator();
  }
});

/**
 * Initialize SIP Calculator
 */
function initSIPCalculator() {
  // Get form elements
  const form = document.getElementById("sip-calculator-form");
  const monthlyInvestment = document.getElementById("monthly-investment");
  const investmentPeriod = document.getElementById("investment-period");
  const expectedReturn = document.getElementById("expected-return");
  const results = document.getElementById("sip-results");

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("sip");
  if (savedValues) {
    monthlyInvestment.value = savedValues.monthlyInvestment;
    investmentPeriod.value = savedValues.investmentPeriod;
    expectedReturn.value = savedValues.expectedReturn;
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const monthlyInvestmentValue = parseFloat(monthlyInvestment.value);
    const investmentPeriodValue = parseInt(investmentPeriod.value);
    const expectedReturnValue = parseFloat(expectedReturn.value);

    // Validate inputs
    const investmentValidation = calculatorUtils.validateNumericInput(
      monthlyInvestmentValue,
      100,
      Number.MAX_SAFE_INTEGER,
      "Please enter a valid amount (minimum ₹100)",
    );

    if (!investmentValidation.valid) {
      calculatorUtils.showError(
        monthlyInvestment,
        investmentValidation.message,
      );
      return;
    }

    const periodValidation = calculatorUtils.validateNumericInput(
      investmentPeriodValue,
      1,
      50,
      "Please enter a valid period between 1 and 50 years",
    );

    if (!periodValidation.valid) {
      calculatorUtils.showError(investmentPeriod, periodValidation.message);
      return;
    }

    const returnValidation = calculatorUtils.validateNumericInput(
      expectedReturnValue,
      1,
      30,
      "Please enter a valid return rate between 1% and 30%",
    );

    if (!returnValidation.valid) {
      calculatorUtils.showError(expectedReturn, returnValidation.message);
      return;
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("sip", {
      monthlyInvestment: monthlyInvestmentValue,
      investmentPeriod: investmentPeriodValue,
      expectedReturn: expectedReturnValue,
    });

    // Calculate SIP returns
    const monthlyRate = expectedReturnValue / (12 * 100);
    const months = investmentPeriodValue * 12;
    const totalInvested = monthlyInvestmentValue * months;

    // Formula: P × ({[1 + i]^n - 1} / i) × (1 + i)
    // Where P is monthly investment, i is monthly interest rate, n is number of months
    const totalValue =
      monthlyInvestmentValue *
      ((Math.pow(1 + monthlyRate, months) - 1) / monthlyRate) *
      (1 + monthlyRate);

    const estimatedReturns = totalValue - totalInvested;

    // Round values to 2 decimal places
    const roundedTotalValue = calculatorUtils.round(totalValue, 2);
    const roundedEstimatedReturns = calculatorUtils.round(estimatedReturns, 2);

    // Display results with modern UI
    displaySipResults(
      totalInvested,
      roundedEstimatedReturns,
      roundedTotalValue,
    );
    results.style.display = "block";

    // Generate and display chart
    generateSipChart(
      monthlyInvestmentValue,
      investmentPeriodValue,
      expectedReturnValue,
      totalInvested,
      roundedTotalValue,
    );

    // Add visual content strategy components
    addSipVisualContent(
      monthlyInvestmentValue,
      investmentPeriodValue,
      expectedReturnValue,
      totalInvested,
      roundedTotalValue,
    );

    // Save calculation to history
    storageManager.saveCalculationHistory("sip", {
      monthlyInvestment: monthlyInvestmentValue,
      investmentPeriod: investmentPeriodValue,
      expectedReturn: expectedReturnValue,
      totalInvested: totalInvested,
      estimatedReturns: roundedEstimatedReturns,
      totalValue: roundedTotalValue,
    });

    // Show in-article ad after calculation
    const inArticleAd = document.getElementById(
      "ad-between-calculator-results",
    );
    if (inArticleAd) {
      inArticleAd.style.display = "block";
    }
  });
}

/**
 * Display SIP results with modern UI components
 */
function displaySipResults(totalInvested, estimatedReturns, totalValue) {
  // Check if we should use modern card layout or fallback to legacy
  const resultsContainer = document.getElementById("sip-results");
  if (!resultsContainer) return;

  // Try to find existing result cards container or create one
  let cardsContainer = resultsContainer.querySelector(".result-cards-grid");
  if (!cardsContainer) {
    // Create modern cards layout
    cardsContainer = document.createElement("div");
    cardsContainer.className = "result-cards-grid";

    // Insert after the h3 title
    const title = resultsContainer.querySelector("h3");
    if (title && title.nextSibling) {
      resultsContainer.insertBefore(cardsContainer, title.nextSibling);
    } else if (title) {
      title.parentNode.insertBefore(cardsContainer, title.nextSibling);
    } else {
      resultsContainer.appendChild(cardsContainer);
    }
  }

  // Clear existing cards
  cardsContainer.innerHTML = "";

  // Create result cards
  const investedCard = calculatorUtils.createResultCard({
    icon: "💰",
    label: "Total Amount Invested",
    value: calculatorUtils.formatCurrency(totalInvested),
    description: "Your total SIP contributions",
    highlight: false,
    animate: true,
  });

  const returnsCard = calculatorUtils.createResultCard({
    icon: "📈",
    label: "Estimated Returns",
    value: calculatorUtils.formatCurrency(estimatedReturns),
    description: "Wealth generated through compounding",
    highlight: false,
    animate: true,
  });

  const totalCard = calculatorUtils.createResultCard({
    icon: "🎯",
    label: "Total Value",
    value: calculatorUtils.formatCurrency(totalValue),
    description: "Your investment corpus at maturity",
    highlight: true,
    animate: true,
  });

  // Add cards to container
  cardsContainer.appendChild(investedCard);
  cardsContainer.appendChild(returnsCard);
  cardsContainer.appendChild(totalCard);

  // Animate the values
  setTimeout(() => {
    calculatorUtils.animateCounter(
      investedCard.querySelector(".result-card-value"),
      totalInvested,
      1200,
      (val) => calculatorUtils.formatCurrency(val),
    );

    calculatorUtils.animateCounter(
      returnsCard.querySelector(".result-card-value"),
      estimatedReturns,
      1500,
      (val) => calculatorUtils.formatCurrency(val),
    );

    calculatorUtils.animateCounter(
      totalCard.querySelector(".result-card-value"),
      totalValue,
      1800,
      (val) => calculatorUtils.formatCurrency(val),
    );
  }, 300);

  // Also update legacy elements if they exist (for backward compatibility)
  const legacyElements = {
    "total-invested": totalInvested,
    "estimated-returns": estimatedReturns,
    "total-value": totalValue,
  };

  Object.entries(legacyElements).forEach(([id, value]) => {
    const element = document.getElementById(id);
    if (element) {
      element.textContent = calculatorUtils.formatCurrency(value);
    }
  });
}

/**
 * Generate SIP growth chart
 */
function generateSipChart(
  monthlyInvestment,
  years,
  rate,
  totalInvested,
  totalValue,
) {
  const chartContainer = document.getElementById("sip-chart-container");
  if (!chartContainer) return;

  chartContainer.innerHTML = ""; // Clear previous chart

  // Add chart title
  const chartTitle = document.createElement("div");
  chartTitle.className = "chart-title";
  chartTitle.textContent = "Investment Growth Visualization";
  chartContainer.appendChild(chartTitle);

  // Create a simple bar chart using HTML/CSS
  const chartEl = document.createElement("div");
  chartEl.className = "simple-chart";

  const investedBar = document.createElement("div");
  investedBar.className = "chart-bar invested";
  investedBar.style.height = "100px"; // Fixed height for invested amount
  investedBar.innerHTML = `<span class="bar-label">Invested<br>${calculatorUtils.formatCurrency(
    totalInvested,
  )}</span>`;

  const returnsBar = document.createElement("div");
  returnsBar.className = "chart-bar returns";
  // Calculate proportional height for returns
  const returnsHeight = (totalValue / totalInvested) * 100;
  returnsBar.style.height = `${Math.min(returnsHeight, 300)}px`; // Cap at 300px
  returnsBar.innerHTML = `<span class="bar-label">Total Value<br>${calculatorUtils.formatCurrency(
    totalValue,
  )}</span>`;

  chartEl.appendChild(investedBar);
  chartEl.appendChild(returnsBar);
  chartContainer.appendChild(chartEl);

  // Add progress ring showing returns percentage
  const returnsPercentage =
    ((totalValue - totalInvested) / totalInvested) * 100;
  const progressContainer = document.createElement("div");
  progressContainer.id = "sip-progress-ring";
  progressContainer.style.marginTop = "2rem";
  chartContainer.appendChild(progressContainer);

  calculatorUtils.createProgressRing(
    Math.min(returnsPercentage, 100),
    "sip-progress-ring",
    {
      size: 140,
      strokeWidth: 10,
      label: "Returns Growth",
      animated: true,
    },
  );
}

/**
 * Add visual content strategy components to SIP calculator
 */
function addSipVisualContent(
  monthlyInvestment,
  years,
  rate,
  totalInvested,
  totalValue,
) {
  // Add SIP vs Lump Sum comparison infographic
  addSipVsLumpSumComparison(monthlyInvestment, years, rate);

  // Add financial journey storytelling
  addSipFinancialJourney(monthlyInvestment, years, totalInvested, totalValue);

  // Add step-by-step process infographic
  addSipProcessInfographic();

  // Add micro-infographics
  addSipMicroInfographics(monthlyInvestment, years, totalValue);

  // Add interactive timeline
  addSipTimelineVisualization(monthlyInvestment, years, rate);

  // Add advanced Chart.js visualizations
  addAdvancedSipCharts(
    monthlyInvestment,
    years,
    rate,
    totalInvested,
    totalValue,
  );
}

/**
 * Create SIP vs Lump Sum comparison infographic
 */
function addSipVsLumpSumComparison(monthlyInvestment, years, rate) {
  // Calculate lump sum equivalent
  const lumpSumAmount = monthlyInvestment * 12 * years;
  const lumpSumFinalValue = lumpSumAmount * Math.pow(1 + rate / 100, years);

  // Calculate SIP values
  const monthlyRate = rate / (12 * 100);
  const months = years * 12;
  const sipFinalValue =
    monthlyInvestment *
    ((Math.pow(1 + monthlyRate, months) - 1) / monthlyRate) *
    (1 + monthlyRate);

  // Find or create container
  let container = document.getElementById("sip-comparison-container");
  if (!container) {
    container = document.createElement("div");
    container.id = "sip-comparison-container";

    // Insert after results section
    const resultsSection = document.getElementById("sip-results");
    if (resultsSection && resultsSection.parentNode) {
      resultsSection.parentNode.insertBefore(
        container,
        resultsSection.nextSibling,
      );
    }
  }

  if (typeof VisualComponents !== "undefined") {
    VisualComponents.createComparisonInfographic({
      containerId: "sip-comparison-container",
      title: "SIP vs Lump Sum Investment Comparison",
      leftSide: {
        theme: "sip",
        icon: "📈",
        title: "SIP Investment",
        metrics: [
          {
            label: "Monthly Investment",
            value: calculatorUtils.formatCurrency(monthlyInvestment),
          },
          {
            label: "Total Investment",
            value: calculatorUtils.formatCurrency(monthlyInvestment * months),
          },
          {
            label: "Final Value",
            value: calculatorUtils.formatCurrency(sipFinalValue),
          },
          {
            label: "Returns",
            value: calculatorUtils.formatCurrency(
              sipFinalValue - monthlyInvestment * months,
            ),
          },
        ],
      },
      rightSide: {
        theme: "lump-sum",
        icon: "💰",
        title: "Lump Sum Investment",
        metrics: [
          {
            label: "Initial Investment",
            value: calculatorUtils.formatCurrency(lumpSumAmount),
          },
          {
            label: "Total Investment",
            value: calculatorUtils.formatCurrency(lumpSumAmount),
          },
          {
            label: "Final Value",
            value: calculatorUtils.formatCurrency(lumpSumFinalValue),
          },
          {
            label: "Returns",
            value: calculatorUtils.formatCurrency(
              lumpSumFinalValue - lumpSumAmount,
            ),
          },
        ],
      },
      insight: `SIP provides rupee-cost averaging benefits and reduces timing risk. In this scenario, ${
        sipFinalValue > lumpSumFinalValue
          ? "SIP generates higher returns"
          : "Lump Sum generates higher returns"
      } due to ${
        sipFinalValue > lumpSumFinalValue
          ? "regular investment discipline"
          : "immediate market exposure"
      }.`,
    });
  }
}

/**
 * Create financial journey storytelling component
 */
function addSipFinancialJourney(
  monthlyInvestment,
  years,
  totalInvested,
  totalValue,
) {
  let container = document.getElementById("sip-journey-container");
  if (!container) {
    container = document.createElement("div");
    container.id = "sip-journey-container";

    // Insert after comparison section
    const comparisonSection = document.getElementById(
      "sip-comparison-container",
    );
    if (comparisonSection && comparisonSection.parentNode) {
      comparisonSection.parentNode.insertBefore(
        container,
        comparisonSection.nextSibling,
      );
    }
  }

  const phases = [
    {
      type: "heavy-interest",
      icon: "🌱",
      title: "Early Years: Building Foundation",
      description: `In the first ${Math.ceil(
        years * 0.3,
      )} years, your monthly ₹${monthlyInvestment.toLocaleString()} investments are building the foundation. Market volatility might seem concerning, but you're accumulating more units when prices are low.`,
      insight: `Patience is key: Early years focus on accumulation, not returns.`,
    },
    {
      type: "turning-point",
      icon: "⚖️",
      title: "Middle Years: Momentum Building",
      description: `Years ${Math.ceil(years * 0.3)} to ${Math.ceil(
        years * 0.7,
      )}: Compounding starts showing its power. Your accumulated corpus begins generating significant returns, and market fluctuations have less impact on your overall portfolio.`,
      insight: `The magic of compounding: Your money starts working harder for you.`,
    },
    {
      type: "wealth-building",
      icon: "🚀",
      title: "Final Years: Wealth Acceleration",
      description: `Final ${Math.ceil(
        years * 0.3,
      )} years: Exponential growth phase. Your corpus of ₹${(
        totalValue / 100000
      ).toFixed(
        1,
      )} lakh is primarily driven by returns rather than contributions. Small market gains translate to large absolute increases.`,
      insight: `Wealth multiplication: Returns now exceed your annual contributions.`,
    },
  ];

  if (typeof VisualComponents !== "undefined") {
    VisualComponents.createFinancialJourney({
      containerId: "sip-journey-container",
      title: "Your SIP Investment Journey",
      character: "investor",
      phases: phases,
    });
  }
}

/**
 * Create step-by-step process infographic
 */
function addSipProcessInfographic() {
  let container = document.getElementById("sip-process-container");
  if (!container) {
    container = document.createElement("div");
    container.id = "sip-process-container";

    // Insert after journey section
    const journeySection = document.getElementById("sip-journey-container");
    if (journeySection && journeySection.parentNode) {
      journeySection.parentNode.insertBefore(
        container,
        journeySection.nextSibling,
      );
    }
  }

  const html = `
    <div class="process-infographic">
      <h3 class="process-title">5 Steps to Start Your SIP Journey</h3>
      <div class="process-steps">
        <div class="process-step fade-in-up">
          <div class="process-step-number">1</div>
          <div class="process-step-icon">🎯</div>
          <h4 class="process-step-title">Set Your Goal</h4>
          <p class="process-step-description">Define your financial objective - retirement, child's education, or wealth creation. Clear goals help determine the right investment amount and timeline.</p>
          <button class="process-step-action" onclick="document.getElementById('investment-goal').focus()">Set Goal</button>
        </div>
        <div class="process-step fade-in-up">
          <div class="process-step-number">2</div>
          <div class="process-step-icon">💰</div>
          <h4 class="process-step-title">Choose Amount</h4>
          <p class="process-step-description">Start with an amount you can comfortably invest monthly. Follow the 20% savings rule - invest 20% of your monthly income through SIPs.</p>
          <button class="process-step-action" onclick="document.getElementById('monthly-investment').focus()">Enter Amount</button>
        </div>
        <div class="process-step fade-in-up">
          <div class="process-step-number">3</div>
          <div class="process-step-icon">⏰</div>
          <h4 class="process-step-title">Select Timeline</h4>
          <p class="process-step-description">Longer investment periods harness the power of compounding. For wealth creation, consider 15+ years. For specific goals, align with your target date.</p>
          <button class="process-step-action" onclick="document.getElementById('investment-period').focus()">Set Timeline</button>
        </div>
        <div class="process-step fade-in-up">
          <div class="process-step-number">4</div>
          <div class="process-step-icon">📊</div>
          <h4 class="process-step-title">Estimate Returns</h4>
          <p class="process-step-description">Use conservative return estimates: 10-12% for large-cap funds, 12-15% for mid-cap funds. Historical data helps set realistic expectations.</p>
          <button class="process-step-action" onclick="document.getElementById('expected-return').focus()">Set Returns</button>
        </div>
        <div class="process-step fade-in-up">
          <div class="process-step-number">5</div>
          <div class="process-step-icon">🚀</div>
          <h4 class="process-step-title">Start Investing</h4>
          <p class="process-step-description">Begin your SIP journey with a reputable fund house. Set up auto-debit for discipline and consider step-up SIPs for inflation protection.</p>
          <button class="process-step-action" onclick="window.open('https://www.amfiindia.com/', '_blank')">Find Funds</button>
        </div>
      </div>
    </div>
  `;

  container.innerHTML = html;
}

/**
 * Create micro-infographics for social sharing
 */
function addSipMicroInfographics(monthlyInvestment, years, totalValue) {
  let container = document.getElementById("sip-micro-container");
  if (!container) {
    container = document.createElement("div");
    container.id = "sip-micro-container";

    // Insert after process section
    const processSection = document.getElementById("sip-process-container");
    if (processSection && processSection.parentNode) {
      processSection.parentNode.insertBefore(
        container,
        processSection.nextSibling,
      );
    }
  }

  const returnsPercentage = (
    ((totalValue - monthlyInvestment * years * 12) /
      (monthlyInvestment * years * 12)) *
    100
  ).toFixed(1);

  if (typeof VisualComponents !== "undefined") {
    VisualComponents.createMicroInfographic({
      containerId: "sip-micro-container",
      theme: "investment",
      hook: "SIP Magic in Action!",
      content: `Investing just ₹${monthlyInvestment.toLocaleString()} monthly for ${years} years`,
      stats: [
        {
          value: `₹${(totalValue / 100000).toFixed(1)}L`,
          label: "Total Wealth Created",
        },
        {
          value: `${returnsPercentage}%`,
          label: "Total Returns Generated",
        },
      ],
    });
  }
}

/**
 * Create interactive timeline visualization
 */
function addSipTimelineVisualization(monthlyInvestment, years, rate) {
  let container = document.getElementById("sip-timeline-container");
  if (!container) {
    container = document.createElement("div");
    container.id = "sip-timeline-container";

    // Insert after micro-infographics section
    const microSection = document.getElementById("sip-micro-container");
    if (microSection && microSection.parentNode) {
      microSection.parentNode.insertBefore(container, microSection.nextSibling);
    }
  }

  // Calculate values at different time points
  const timePoints = [];
  const monthlyRate = rate / (12 * 100);

  for (
    let year = 0;
    year <= years;
    year += Math.max(1, Math.floor(years / 5))
  ) {
    const months = year * 12;
    let value = 0;

    if (year > 0) {
      value =
        monthlyInvestment *
        ((Math.pow(1 + monthlyRate, months) - 1) / monthlyRate) *
        (1 + monthlyRate);
    }

    timePoints.push({
      year: year,
      label: `Year ${year}`,
      value: calculatorUtils.formatCurrency(value),
    });
  }

  if (typeof VisualComponents !== "undefined") {
    VisualComponents.createTimelineVisualization({
      containerId: "sip-timeline-container",
      title: "Your SIP Growth Timeline",
      points: timePoints,
    });
  }
}

/**
 * Add advanced Chart.js visualizations for SIP calculator
 */
function addAdvancedSipCharts(
  monthlyInvestment,
  years,
  rate,
  totalInvested,
  totalValue,
) {
  // Add investment growth projection chart
  addSipGrowthProjectionChart(monthlyInvestment, years, rate);

  // Add SIP vs Lump Sum comparison chart
  addSipVsLumpSumChart(monthlyInvestment, years, rate);

  // Add investment dashboard
  addSipInvestmentDashboard(
    monthlyInvestment,
    years,
    rate,
    totalInvested,
    totalValue,
  );
}

/**
 * Create SIP growth projection chart using Chart.js
 */
function addSipGrowthProjectionChart(monthlyInvestment, years, rate) {
  let container = document.getElementById("sip-growth-chart-container");
  if (!container) {
    container = document.createElement("div");
    container.id = "sip-growth-chart-container";

    // Insert after timeline section
    const timelineSection = document.getElementById("sip-timeline-container");
    if (timelineSection && timelineSection.parentNode) {
      timelineSection.parentNode.insertBefore(
        container,
        timelineSection.nextSibling,
      );
    }
  }

  if (typeof VisualComponents !== "undefined") {
    VisualComponents.createInvestmentGrowthChart({
      containerId: "sip-growth-chart-container",
      monthlyInvestment: monthlyInvestment,
      years: years,
      expectedReturn: rate,
      investmentType: "SIP",
    });
  }
}

/**
 * Create SIP vs Lump Sum comparison chart
 */
function addSipVsLumpSumChart(monthlyInvestment, years, rate) {
  let container = document.getElementById("sip-vs-lumpsum-chart-container");
  if (!container) {
    container = document.createElement("div");
    container.id = "sip-vs-lumpsum-chart-container";

    // Insert after growth chart section
    const growthSection = document.getElementById("sip-growth-chart-container");
    if (growthSection && growthSection.parentNode) {
      growthSection.parentNode.insertBefore(
        container,
        growthSection.nextSibling,
      );
    }
  }

  // Calculate SIP and Lump Sum values for comparison
  const monthlyRate = rate / (12 * 100);
  const months = years * 12;
  const lumpSumAmount = monthlyInvestment * months;

  const sipData = [];
  const lumpSumData = [];
  const labels = [];

  for (let year = 1; year <= years; year++) {
    const currentMonths = year * 12;

    // SIP calculation
    const sipValue =
      monthlyInvestment *
      ((Math.pow(1 + monthlyRate, currentMonths) - 1) / monthlyRate) *
      (1 + monthlyRate);

    // Lump Sum calculation
    const lumpSumValue = lumpSumAmount * Math.pow(1 + rate / 100, year);

    sipData.push(Math.round(sipValue));
    lumpSumData.push(Math.round(lumpSumValue));
    labels.push(`Year ${year}`);
  }

  const chartData = {
    labels: labels,
    datasets: [
      {
        label: "SIP Investment",
        data: sipData,
        borderColor: "rgb(56, 176, 0)",
        backgroundColor: "rgba(56, 176, 0, 0.1)",
        borderWidth: 3,
        fill: false,
        tension: 0.4,
        pointBackgroundColor: "rgb(56, 176, 0)",
        pointBorderColor: "#fff",
        pointBorderWidth: 2,
        pointRadius: 5,
      },
      {
        label: "Lump Sum Investment",
        data: lumpSumData,
        borderColor: "rgb(67, 97, 238)",
        backgroundColor: "rgba(67, 97, 238, 0.1)",
        borderWidth: 3,
        fill: false,
        tension: 0.4,
        pointBackgroundColor: "rgb(67, 97, 238)",
        pointBorderColor: "#fff",
        pointBorderWidth: 2,
        pointRadius: 5,
      },
    ],
  };

  if (typeof VisualComponents !== "undefined") {
    VisualComponents.createAdvancedChart({
      containerId: "sip-vs-lumpsum-chart-container",
      title: "SIP vs Lump Sum: Growth Comparison Over Time",
      type: "line",
      data: chartData,
      options: {
        plugins: {
          legend: {
            position: "top",
          },
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function (value) {
                return "₹" + (value / 100000).toFixed(1) + "L";
              },
            },
          },
        },
      },
    });
  }
}

/**
 * Create investment dashboard with key metrics
 */
function addSipInvestmentDashboard(
  monthlyInvestment,
  years,
  rate,
  totalInvested,
  totalValue,
) {
  let container = document.getElementById("sip-dashboard-container");
  if (!container) {
    container = document.createElement("div");
    container.id = "sip-dashboard-container";

    // Insert after comparison chart section
    const comparisonSection = document.getElementById(
      "sip-vs-lumpsum-chart-container",
    );
    if (comparisonSection && comparisonSection.parentNode) {
      comparisonSection.parentNode.insertBefore(
        container,
        comparisonSection.nextSibling,
      );
    }
  }

  const totalReturns = totalValue - totalInvested;
  const annualizedReturn = (
    (Math.pow(totalValue / totalInvested, 1 / years) - 1) *
    100
  ).toFixed(2);
  const monthlyReturn = (totalReturns / (years * 12)).toFixed(0);

  const html = `
    <div class="investment-dashboard">
      <div class="dashboard-card">
        <div class="dashboard-card-title">
          <span>📊</span>
          Investment Summary
        </div>
        <div class="dashboard-metric">
          <span class="dashboard-metric-label">Monthly SIP</span>
          <span class="dashboard-metric-value">${calculatorUtils.formatCurrency(
            monthlyInvestment,
          )}</span>
        </div>
        <div class="dashboard-metric">
          <span class="dashboard-metric-label">Investment Period</span>
          <span class="dashboard-metric-value">${years} years</span>
        </div>
        <div class="dashboard-metric">
          <span class="dashboard-metric-label">Expected Return</span>
          <span class="dashboard-metric-value">${rate}% p.a.</span>
        </div>
        <div class="dashboard-metric dashboard-metric-highlight">
          <span class="dashboard-metric-label">Total Investment</span>
          <span class="dashboard-metric-value">${calculatorUtils.formatCurrency(
            totalInvested,
          )}</span>
        </div>
      </div>

      <div class="dashboard-card">
        <div class="dashboard-card-title">
          <span>💰</span>
          Wealth Creation
        </div>
        <div class="dashboard-metric">
          <span class="dashboard-metric-label">Final Corpus</span>
          <span class="dashboard-metric-value">${calculatorUtils.formatCurrency(
            totalValue,
          )}</span>
        </div>
        <div class="dashboard-metric">
          <span class="dashboard-metric-label">Total Returns</span>
          <span class="dashboard-metric-value">${calculatorUtils.formatCurrency(
            totalReturns,
          )}</span>
        </div>
        <div class="dashboard-metric">
          <span class="dashboard-metric-label">Annualized Return</span>
          <span class="dashboard-metric-value">${annualizedReturn}%</span>
        </div>
        <div class="dashboard-metric dashboard-metric-highlight">
          <span class="dashboard-metric-label">Monthly Return</span>
          <span class="dashboard-metric-value">₹${monthlyReturn}</span>
        </div>
      </div>
    </div>
  `;

  container.innerHTML = html;
}

/**
 * Initialize Compound Interest Calculator
 */
function initCompoundInterestCalculator() {
  // Get form elements
  const form = document.getElementById("compound-interest-form");
  const principalAmount = document.getElementById("principal-amount");
  const interestRate = document.getElementById("interest-rate");
  const timePeriod = document.getElementById("time-period");
  const compoundingFrequency = document.getElementById("compounding-frequency");
  const results = document.getElementById("compound-interest-results");

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("compound-interest");
  if (savedValues) {
    principalAmount.value = savedValues.principalAmount;
    interestRate.value = savedValues.interestRate;
    timePeriod.value = savedValues.timePeriod;
    compoundingFrequency.value = savedValues.compoundingFrequency;
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const principalValue = parseFloat(principalAmount.value);
    const rateValue = parseFloat(interestRate.value);
    const timeValue = parseInt(timePeriod.value);
    const frequencyValue = parseInt(compoundingFrequency.value);

    // Validate inputs - Principal amount validation removed

    const rateValidation = calculatorUtils.validateNumericInput(
      rateValue,
      0.1,
      50,
      "Please enter a valid interest rate between 0.1% and 50%",
    );

    if (!rateValidation.valid) {
      calculatorUtils.showError(interestRate, rateValidation.message);
      return;
    }

    const timeValidation = calculatorUtils.validateNumericInput(
      timeValue,
      1,
      50,
      "Please enter a valid time period between 1 and 50 years",
    );

    if (!timeValidation.valid) {
      calculatorUtils.showError(timePeriod, timeValidation.message);
      return;
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("compound-interest", {
      principalAmount: principalValue,
      interestRate: rateValue,
      timePeriod: timeValue,
      compoundingFrequency: frequencyValue,
    });

    // Calculate compound interest
    // Formula: P(1 + r/n)^(nt)
    // Where P is principal, r is rate (decimal), n is compounding frequency, t is time in years
    const rate = rateValue / 100;
    const finalAmount =
      principalValue *
      Math.pow(1 + rate / frequencyValue, frequencyValue * timeValue);
    const interestEarned = finalAmount - principalValue;

    // Round values to 2 decimal places
    const roundedFinalAmount = calculatorUtils.round(finalAmount, 2);
    const roundedInterestEarned = calculatorUtils.round(interestEarned, 2);

    // Display results
    document.getElementById("result-principal").textContent =
      calculatorUtils.formatCurrency(principalValue);
    document.getElementById("interest-earned").textContent =
      calculatorUtils.formatCurrency(roundedInterestEarned);
    document.getElementById("final-amount").textContent =
      calculatorUtils.formatCurrency(roundedFinalAmount);
    results.style.display = "block";

    // Generate and display chart
    generateCompoundChart(
      principalValue,
      roundedFinalAmount,
      timeValue,
      frequencyValue,
      rateValue,
    );

    // Save calculation to history
    storageManager.saveCalculationHistory("compound-interest", {
      principalAmount: principalValue,
      interestRate: rateValue,
      timePeriod: timeValue,
      compoundingFrequency: frequencyValue,
      interestEarned: roundedInterestEarned,
      finalAmount: roundedFinalAmount,
    });

    // Show in-article ad after calculation
    const inArticleAd = document.getElementById(
      "ad-between-calculator-results",
    );
    if (inArticleAd) {
      inArticleAd.style.display = "block";
    }
  });
}

/**
 * Generate compound interest growth chart
 */
function generateCompoundChart(principal, finalAmount, years, frequency, rate) {
  const chartContainer = document.getElementById("compound-chart-container");
  if (!chartContainer) return;

  chartContainer.innerHTML = ""; // Clear previous chart

  // Create a simple growth chart using HTML/CSS
  const chartEl = document.createElement("div");
  chartEl.className = "growth-chart";

  // Create year markers and value points
  for (let i = 0; i <= years; i += Math.max(1, Math.floor(years / 5))) {
    // Calculate the value at this year using the same compound interest formula
    const yearValue =
      i === 0
        ? principal
        : principal * Math.pow(1 + rate / 100 / frequency, frequency * i);

    // Create a point on the chart
    const point = document.createElement("div");
    point.className = "chart-point";
    // Position horizontally based on year
    point.style.left = `${(i / years) * 100}%`;
    // Position vertically based on value (inverted, as 0 is at the top in CSS)
    const verticalPosition = 100 - (yearValue / finalAmount) * 100;
    point.style.bottom = `${verticalPosition}%`;

    // Add label
    point.innerHTML = `<span class="point-label">Year ${i}<br>${calculatorUtils.formatCurrency(
      yearValue,
    )}</span>`;

    chartEl.appendChild(point);
  }

  chartContainer.appendChild(chartEl);
}

/**
 * Initialize Lump Sum Calculator
 */
function initLumpSumCalculator() {
  // Get form elements
  const form = document.getElementById("lump-sum-form");
  const investmentAmount = document.getElementById("investment-amount");
  const investmentPeriod = document.getElementById("investment-period");
  const expectedReturn = document.getElementById("expected-return");
  const results = document.getElementById("lump-sum-results");

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("lump-sum");
  if (savedValues) {
    investmentAmount.value = savedValues.investmentAmount;
    investmentPeriod.value = savedValues.investmentPeriod;
    expectedReturn.value = savedValues.expectedReturn;
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const investmentAmountValue = parseFloat(investmentAmount.value);
    const investmentPeriodValue = parseInt(investmentPeriod.value);
    const expectedReturnValue = parseFloat(expectedReturn.value);

    // Validate inputs
    const amountValidation = calculatorUtils.validateNumericInput(
      investmentAmountValue,
      1000,
      Number.MAX_SAFE_INTEGER,
      "Please enter a valid amount (minimum ₹1,000)",
    );

    if (!amountValidation.valid) {
      calculatorUtils.showError(investmentAmount, amountValidation.message);
      return;
    }

    const periodValidation = calculatorUtils.validateNumericInput(
      investmentPeriodValue,
      1,
      50,
      "Please enter a valid period (1-50 years)",
    );

    if (!periodValidation.valid) {
      calculatorUtils.showError(investmentPeriod, periodValidation.message);
      return;
    }

    const returnValidation = calculatorUtils.validateNumericInput(
      expectedReturnValue,
      1,
      30,
      "Please enter a valid return rate (1-30%)",
    );

    if (!returnValidation.valid) {
      calculatorUtils.showError(expectedReturn, returnValidation.message);
      return;
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("lump-sum", {
      investmentAmount: investmentAmountValue,
      investmentPeriod: investmentPeriodValue,
      expectedReturn: expectedReturnValue,
    });

    // Calculate lump sum investment returns
    // Formula: A = P(1 + r)^t
    // Where A is final amount, P is principal, r is rate, t is time
    const rate = expectedReturnValue / 100;
    const finalAmount =
      investmentAmountValue * Math.pow(1 + rate, investmentPeriodValue);
    const estimatedReturns = finalAmount - investmentAmountValue;

    // Round values to 2 decimal places
    const roundedFinalAmount = calculatorUtils.round(finalAmount, 2);
    const roundedEstimatedReturns = calculatorUtils.round(estimatedReturns, 2);

    // Display results
    document.getElementById("invested-amount").textContent =
      calculatorUtils.formatCurrency(investmentAmountValue);
    document.getElementById("estimated-returns").textContent =
      calculatorUtils.formatCurrency(roundedEstimatedReturns);
    document.getElementById("total-value").textContent =
      calculatorUtils.formatCurrency(roundedFinalAmount);
    results.style.display = "block";

    // Generate and display chart
    generateLumpSumChart(
      investmentAmountValue,
      roundedEstimatedReturns,
      roundedFinalAmount,
    );

    // Save calculation to history
    storageManager.saveCalculationHistory("lump-sum", {
      investmentAmount: investmentAmountValue,
      investmentPeriod: investmentPeriodValue,
      expectedReturn: expectedReturnValue,
      finalAmount: roundedFinalAmount,
      estimatedReturns: roundedEstimatedReturns,
    });

    // Show in-article ad after calculation
    const inArticleAd = document.getElementById(
      "ad-between-calculator-results",
    );
    if (inArticleAd) {
      inArticleAd.style.display = "block";
    }
  });
}

/**
 * Generate chart for Lump Sum Calculator
 */
function generateLumpSumChart(investmentAmount, estimatedReturns, totalValue) {
  const chartContainer = document.getElementById("lump-sum-chart-container");

  // Clear previous chart if any
  chartContainer.innerHTML = "";

  // Create chart elements
  const chartEl = document.createElement("div");
  chartEl.className = "pie-chart";

  // Create principal slice
  const principalSlice = document.createElement("div");
  principalSlice.className = "chart-slice principal-slice";
  principalSlice.style.width = (investmentAmount / totalValue) * 100 + "%";

  // Create returns slice
  const returnsSlice = document.createElement("div");
  returnsSlice.className = "chart-slice returns-slice";
  returnsSlice.style.width = (estimatedReturns / totalValue) * 100 + "%";

  // Create legend
  const legend = document.createElement("div");
  legend.className = "chart-legend";

  const principalLegend = document.createElement("div");
  principalLegend.className = "legend-item";
  principalLegend.innerHTML =
    '<span class="legend-color principal-color"></span> Principal: ' +
    calculatorUtils.formatCurrency(investmentAmount) +
    " (" +
    calculatorUtils.round((investmentAmount / totalValue) * 100, 1) +
    "%)";

  const returnsLegend = document.createElement("div");
  returnsLegend.className = "legend-item";
  returnsLegend.innerHTML =
    '<span class="legend-color returns-color"></span> Returns: ' +
    calculatorUtils.formatCurrency(estimatedReturns) +
    " (" +
    calculatorUtils.round((estimatedReturns / totalValue) * 100, 1) +
    "%)";

  // Assemble chart
  chartEl.appendChild(principalSlice);
  chartEl.appendChild(returnsSlice);
  legend.appendChild(principalLegend);
  legend.appendChild(returnsLegend);

  chartContainer.appendChild(chartEl);
  chartContainer.appendChild(legend);
}

/**
 * Initialize Investment Goal Calculator
 */
function initGoalCalculator() {
  // Get form elements
  const form = document.getElementById("goal-calculator-form");
  const targetAmount = document.getElementById("target-amount");
  const timeHorizon = document.getElementById("time-horizon");
  const expectedReturn = document.getElementById("expected-return");
  const investmentFrequency = document.getElementById("investment-frequency");
  const results = document.getElementById("goal-results");

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("goal-calculator");
  if (savedValues) {
    targetAmount.value = savedValues.targetAmount;
    timeHorizon.value = savedValues.timeHorizon;
    expectedReturn.value = savedValues.expectedReturn;
    investmentFrequency.value = savedValues.investmentFrequency;
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const targetAmountValue = parseFloat(targetAmount.value);
    const timeHorizonValue = parseInt(timeHorizon.value);
    const expectedReturnValue = parseFloat(expectedReturn.value);
    const investmentFrequencyValue = parseInt(investmentFrequency.value);

    // Validate inputs
    const targetValidation = calculatorUtils.validateNumericInput(
      targetAmountValue,
      10000,
      Number.MAX_SAFE_INTEGER,
      "Please enter a valid target amount (minimum ₹10,000)",
    );

    if (!targetValidation.valid) {
      calculatorUtils.showError(targetAmount, targetValidation.message);
      return;
    }

    const timeValidation = calculatorUtils.validateNumericInput(
      timeHorizonValue,
      1,
      50,
      "Please enter a valid time horizon (1-50 years)",
    );

    if (!timeValidation.valid) {
      calculatorUtils.showError(timeHorizon, timeValidation.message);
      return;
    }

    const returnValidation = calculatorUtils.validateNumericInput(
      expectedReturnValue,
      1,
      30,
      "Please enter a valid return rate (1-30%)",
    );

    if (!returnValidation.valid) {
      calculatorUtils.showError(expectedReturn, returnValidation.message);
      return;
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("goal-calculator", {
      targetAmount: targetAmountValue,
      timeHorizon: timeHorizonValue,
      expectedReturn: expectedReturnValue,
      investmentFrequency: investmentFrequencyValue,
    });

    // Calculate required investment amount
    // Formula: PMT = FV / {[(1 + r)^n - 1] / r}
    // Where PMT is periodic payment, FV is future value, r is periodic rate, n is number of periods
    const periodicRate = expectedReturnValue / 100 / investmentFrequencyValue;
    const totalPeriods = timeHorizonValue * investmentFrequencyValue;
    const requiredInvestment =
      targetAmountValue /
      ((Math.pow(1 + periodicRate, totalPeriods) - 1) / periodicRate);

    // Calculate total investment and returns
    const totalInvestment = requiredInvestment * totalPeriods;
    const estimatedReturns = targetAmountValue - totalInvestment;

    // Round values to 2 decimal places
    const roundedRequiredInvestment = calculatorUtils.round(
      requiredInvestment,
      2,
    );
    const roundedTotalInvestment = calculatorUtils.round(totalInvestment, 2);
    const roundedEstimatedReturns = calculatorUtils.round(estimatedReturns, 2);

    // Display results
    document.getElementById("result-target-amount").textContent =
      calculatorUtils.formatCurrency(targetAmountValue);
    document.getElementById("result-time-horizon").textContent =
      timeHorizonValue + " years";
    document.getElementById("required-monthly-investment").textContent =
      investmentFrequencyValue === 12
        ? calculatorUtils.formatCurrency(roundedRequiredInvestment) +
          " per month"
        : calculatorUtils.formatCurrency(roundedRequiredInvestment) +
          " per year";
    document.getElementById("total-investment").textContent =
      calculatorUtils.formatCurrency(roundedTotalInvestment);
    document.getElementById("estimated-returns").textContent =
      calculatorUtils.formatCurrency(roundedEstimatedReturns);
    results.style.display = "block";

    // Generate and display chart
    generateGoalChart(
      roundedTotalInvestment,
      roundedEstimatedReturns,
      targetAmountValue,
    );

    // Save calculation to history
    storageManager.saveCalculationHistory("goal-calculator", {
      targetAmount: targetAmountValue,
      timeHorizon: timeHorizonValue,
      expectedReturn: expectedReturnValue,
      investmentFrequency: investmentFrequencyValue,
      requiredInvestment: roundedRequiredInvestment,
      totalInvestment: roundedTotalInvestment,
      estimatedReturns: roundedEstimatedReturns,
    });

    // Show in-article ad after calculation
    const inArticleAd = document.getElementById(
      "ad-between-calculator-results",
    );
    if (inArticleAd) {
      inArticleAd.style.display = "block";
    }
  });
}

/**
 * Generate chart for Investment Goal Calculator
 */
function generateGoalChart(totalInvestment, estimatedReturns, targetAmount) {
  const chartContainer = document.getElementById("goal-chart-container");

  // Clear previous chart if any
  chartContainer.innerHTML = "";

  // Create chart elements
  const chartEl = document.createElement("div");
  chartEl.className = "pie-chart";

  // Create investment slice
  const investmentSlice = document.createElement("div");
  investmentSlice.className = "chart-slice principal-slice";
  investmentSlice.style.width = (totalInvestment / targetAmount) * 100 + "%";

  // Create returns slice
  const returnsSlice = document.createElement("div");
  returnsSlice.className = "chart-slice returns-slice";
  returnsSlice.style.width = (estimatedReturns / targetAmount) * 100 + "%";

  // Create legend
  const legend = document.createElement("div");
  legend.className = "chart-legend";

  const investmentLegend = document.createElement("div");
  investmentLegend.className = "legend-item";
  investmentLegend.innerHTML =
    '<span class="legend-color principal-color"></span> Total Investment: ' +
    calculatorUtils.formatCurrency(totalInvestment) +
    " (" +
    calculatorUtils.round((totalInvestment / targetAmount) * 100, 1) +
    "%)";

  const returnsLegend = document.createElement("div");
  returnsLegend.className = "legend-item";
  returnsLegend.innerHTML =
    '<span class="legend-color returns-color"></span> Returns: ' +
    calculatorUtils.formatCurrency(estimatedReturns) +
    " (" +
    calculatorUtils.round((estimatedReturns / targetAmount) * 100, 1) +
    "%)";

  // Assemble chart
  chartEl.appendChild(investmentSlice);
  chartEl.appendChild(returnsSlice);
  legend.appendChild(investmentLegend);
  legend.appendChild(returnsLegend);

  chartContainer.appendChild(chartEl);
  chartContainer.appendChild(legend);
}
