<svg width="400" height="250" viewBox="0 0 400 250" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="calculatorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#43e97b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#38f9d7;stop-opacity:1" />
    </linearGradient>
    <style>
      .title-text { font-family: 'Poppins', sans-serif; font-weight: 700; font-size: 20px; fill: white; }
      .category-text { font-family: 'Poppins', sans-serif; font-weight: 500; font-size: 12px; fill: white; opacity: 0.9; text-transform: uppercase; letter-spacing: 1px; }
      .brand-text { font-family: 'Poppins', sans-serif; font-weight: 500; font-size: 14px; fill: white; opacity: 0.8; }
    </style>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="250" rx="12" fill="url(#calculatorGradient)"/>
  
  <!-- Decorative circles -->
  <circle cx="320" cy="50" r="2" fill="white" opacity="0.3"/>
  <circle cx="360" cy="80" r="1.5" fill="white" opacity="0.2"/>
  <circle cx="340" cy="120" r="1" fill="white" opacity="0.4"/>
  
  <!-- Calculator icon -->
  <g transform="translate(340, 20)" fill="white" opacity="0.3">
    <rect x="2" y="2" width="20" height="20" rx="2" fill="none" stroke="currentColor" stroke-width="1.5"/>
    <rect x="5" y="6" width="2" height="2" fill="currentColor"/>
    <rect x="9" y="6" width="2" height="2" fill="currentColor"/>
    <rect x="13" y="6" width="2" height="2" fill="currentColor"/>
    <rect x="17" y="6" width="2" height="2" fill="currentColor"/>
    <rect x="5" y="10" width="2" height="2" fill="currentColor"/>
    <rect x="9" y="10" width="2" height="2" fill="currentColor"/>
    <rect x="13" y="10" width="2" height="2" fill="currentColor"/>
    <rect x="17" y="10" width="2" height="6" fill="currentColor"/>
    <rect x="5" y="14" width="2" height="2" fill="currentColor"/>
    <rect x="9" y="14" width="2" height="2" fill="currentColor"/>
    <rect x="13" y="14" width="2" height="2" fill="currentColor"/>
    <rect x="5" y="18" width="6" height="2" fill="currentColor"/>
    <rect x="13" y="18" width="2" height="2" fill="currentColor"/>
  </g>
  
  <!-- Content -->
  <text x="30" y="50" class="category-text">Calculator Guides</text>
  <text x="30" y="80" class="title-text">How to Choose the</text>
  <text x="30" y="105" class="title-text">Right Calculator</text>
  
  <!-- Brand -->
  <text x="270" y="220" class="brand-text">CalculatorSuites</text>
</svg>
