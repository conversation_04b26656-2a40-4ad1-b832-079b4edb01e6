/* Responsive styles for Calculator Suites */

/* Base styles for mobile first approach */
/* Extra small devices (phones, less than 480px) */
@media (max-width: 479px) {
  .container {
    padding: 0 0.75rem;
  }

  h1 {
    font-size: 1.75rem;
    line-height: 1.3;
  }

  h2 {
    font-size: 1.5rem;
  }

  h3 {
    font-size: 1.25rem;
  }

  .hero-section {
    padding: 1.5rem 0;
  }

  .hero-content h1 {
    font-size: 1.75rem;
  }

  .hero-subtitle {
    font-size: 0.9rem;
  }

  .section {
    padding: 2rem 0;
  }

  .calculator-container {
    padding: 1.25rem 0.75rem;
  }

  .grid {
    gap: 1rem;
  }

  .calculator-card {
    margin-bottom: 1rem;
  }

  .footer-grid {
    display: block;
  }

  .footer-column {
    margin-bottom: 1.5rem;
  }

  .breadcrumb-container {
    padding: 0.5rem 0;
  }

  .breadcrumb {
    font-size: 0.8rem;
  }
}

/* Small devices (landscape phones, 480px to 639px) */
@media (min-width: 480px) and (max-width: 639px) {
  .container {
    padding: 0 1rem;
  }

  .hero-section {
    padding: 1.75rem 0;
  }

  .calculator-container {
    padding: 1.5rem 1rem;
  }
}

/* Small devices (landscape phones, 640px and up) */
@media (min-width: 640px) {
  .container {
    padding: 0 1.5rem;
  }

  .calculator-container {
    padding: 2rem;
  }

  .grid-col-sm-6 {
    grid-column: span 6;
  }

  .calculator-card {
    height: 100%;
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
  .grid-col-md-4 {
    grid-column: span 4;
  }

  .grid-col-md-6 {
    grid-column: span 6;
  }

  .grid-col-md-8 {
    grid-column: span 8;
  }

  .form-row {
    display: flex;
    gap: 1rem;
  }

  .form-row .form-group {
    flex: 1;
  }

  .site-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .mobile-menu-toggle {
    display: none;
  }

  .nav-menu {
    display: flex;
  }

  .ad-container.ad-leaderboard {
    display: block;
  }
}

/* Large devices (desktops, 1024px and up) */
@media (min-width: 1024px) {
  .grid-col-lg-3 {
    grid-column: span 3;
  }

  .grid-col-lg-4 {
    grid-column: span 4;
  }

  .grid-col-lg-8 {
    grid-column: span 8;
  }

  .grid-col-lg-9 {
    grid-column: span 9;
  }

  .calculator-container {
    padding: 2.5rem;
  }

  .hero-section {
    padding: 5rem 0;
  }

  .hero-content h1 {
    font-size: 3rem;
  }

  .footer-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }
}

/* Extra large devices (large desktops, 1280px and up) */
@media (min-width: 1280px) {
  .container {
    max-width: 1200px;
  }

  .hero-content h1 {
    font-size: 3.5rem;
  }
}

/* Mobile-specific styles */
@media (max-width: 767px) {
  body {
    font-size: 16px;
  }

  .container {
    padding: 0 1rem;
    width: 100%;
    box-sizing: border-box;
  }

  /* Improved mobile navigation */
  .nav-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    z-index: 100;
    max-height: 80vh;
    overflow-y: auto;
  }

  .nav-menu.active {
    display: block;
  }

  .nav-item {
    margin: 0 0 1rem 0;
    width: 100%;
  }

  .nav-link {
    padding: 0.75rem 0;
    display: block;
    width: 100%;
  }

  .dropdown-menu {
    position: static;
    box-shadow: none;
    padding-left: 1rem;
    display: none;
    width: 100%;
    margin-top: 0.5rem;
  }

  .dropdown-menu li {
    margin-bottom: 0.5rem;
  }

  .dropdown-menu a {
    padding: 0.75rem 0.5rem;
  }

  .has-dropdown.active .dropdown-menu {
    display: block;
  }

  /* Improved mobile menu toggle */
  .mobile-menu-toggle {
    padding: 0.75rem;
    margin-right: -0.75rem;
  }

  .hamburger-icon,
  .hamburger-icon::before,
  .hamburger-icon::after {
    transition: all 0.3s ease;
  }

  /* Grid system improvements */
  .grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 1rem;
  }

  [class^="grid-col-"] {
    grid-column: span 12;
  }

  /* Calculator cards */
  .calculator-card {
    margin-bottom: 1.5rem;
    padding: 1.25rem;
  }

  .card-icon {
    width: 48px;
    height: 48px;
  }

  /* Footer improvements */
  .footer-column {
    margin-bottom: 2rem;
  }

  .footer-bottom {
    text-align: center;
    padding-top: 1rem;
  }

  /* Charts and data visualization */
  .simple-chart {
    gap: 1rem;
    height: 250px;
  }

  .chart-bar {
    width: 60px;
  }

  .pie-chart {
    width: 180px;
    height: 180px;
    margin: 0 auto 1.5rem;
  }

  .breakdown-legend {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  /* Improve form elements on mobile */
  input,
  select,
  textarea,
  button {
    font-size: 16px;
    /* Prevents iOS zoom on focus */
    max-width: 100%;
    padding: 0.75rem;
    border-radius: 0.25rem;
  }

  .form-group {
    margin-bottom: 1.25rem;
  }

  /* Improve calculator container spacing */
  .calculator-container {
    padding: 1.5rem 1rem;
    margin-bottom: 1.5rem;
  }

  /* Adjust hero section for mobile */
  .hero-section {
    padding: 2rem 0;
    text-align: center;
  }

  .hero-content h1 {
    font-size: 2rem;
    line-height: 1.3;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-cta {
    margin-top: 1.5rem;
  }

  /* Improve table responsiveness */
  table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
  }

  th,
  td {
    padding: 0.5rem;
  }

  /* Improve tap targets */
  .nav-link,
  .card-link,
  .footer-links a,
  button {
    min-height: 44px;
    display: inline-flex;
    align-items: center;
  }

  /* Sidebar improvements */
  .sidebar {
    margin-top: 2rem;
  }

  .sidebar-section {
    margin-bottom: 1.5rem;
  }

  /* Breadcrumb improvements */
  .breadcrumb-container {
    padding: 0.5rem 0;
    margin-bottom: 1rem;
  }

  /* Content sections */
  .content-section {
    margin-bottom: 1.5rem;
  }

  .section-title {
    font-size: 1.5rem;
    margin-bottom: 1.25rem;
  }

  /* FAQ sections */
  .faq-item {
    margin-bottom: 1.25rem;
  }

  .faq-item h3 {
    font-size: 1.1rem;
  }

  /* Category Navigation Mobile Styles */
  .category-navigation {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .category-navigation h2 {
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
  }

  .category-navigation>ul>li {
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
  }

  .category-navigation>ul>li>a {
    font-size: 1rem;
    padding: 0.25rem 0;
  }

  .category-navigation ul ul li {
    margin-bottom: 0.4rem;
    padding-left: 0.75rem;
  }

  .category-navigation ul ul li a {
    font-size: 0.9rem;
    line-height: 1.3;
  }
}