/**
 * Tax Calculator Scripts
 */

document.addEventListener("DOMContentLoaded", function () {
  // Initialize GST Calculator if it exists on the page
  const gstCalculatorForm = document.getElementById("gst-calculator-form");
  if (gstCalculatorForm) {
    initGSTCalculator();
  }

  // Initialize Income Tax Calculator if it exists on the page
  const incomeTaxCalculatorForm = document.getElementById(
    "income-tax-calculator-form",
  );
  if (incomeTaxCalculatorForm) {
    initIncomeTaxCalculator();
  }

  // Initialize Tax Comparison Tool if it exists on the page
  const taxComparisonForm = document.getElementById("tax-comparison-form");
  if (taxComparisonForm) {
    initTaxComparisonTool();
  }
});

/**
 * Initialize GST Calculator
 */
function initGSTCalculator() {
  // Get form elements
  const form = document.getElementById("gst-calculator-form");
  const calculationType = document.getElementById("calculation-type");
  const amount = document.getElementById("amount");
  const gstRate = document.getElementById("gst-rate");
  const customRateGroup = document.getElementById("custom-rate-group");
  const customRate = document.getElementById("custom-rate");
  const results = document.getElementById("gst-results");

  // Display custom rate field when "Custom Rate" is selected
  gstRate.addEventListener("change", function () {
    if (this.value === "custom") {
      customRateGroup.style.display = "block";
      customRate.setAttribute("required", "required");
    } else {
      customRateGroup.style.display = "none";
      customRate.removeAttribute("required");
    }
  });

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("gst");
  if (savedValues) {
    calculationType.value = savedValues.calculationType;
    amount.value = savedValues.amount;
    gstRate.value = savedValues.gstRate;

    if (gstRate.value === "custom") {
      customRateGroup.style.display = "block";
      customRate.value = savedValues.customRate;
    }
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const amountValue = parseFloat(amount.value);
    let rateValue;

    if (gstRate.value === "custom") {
      rateValue = parseFloat(customRate.value) / 100;
    } else {
      rateValue = parseFloat(gstRate.value);
    }

    // Validate inputs
    const amountValidation = calculatorUtils.validateNumericInput(
      amountValue,
      0,
      Number.MAX_SAFE_INTEGER,
      "Please enter a valid amount greater than 0",
    );

    if (!amountValidation.valid) {
      calculatorUtils.showError(amount, amountValidation.message);
      return;
    }

    if (gstRate.value === "custom") {
      const rateValidation = calculatorUtils.validateNumericInput(
        parseFloat(customRate.value),
        0,
        100,
        "Please enter a valid rate between 0 and 100",
      );

      if (!rateValidation.valid) {
        calculatorUtils.showError(customRate, rateValidation.message);
        return;
      }
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("gst", {
      calculationType: calculationType.value,
      amount: amountValue,
      gstRate: gstRate.value,
      customRate: customRate.value,
    });

    // Calculate GST
    let originalAmount, gstAmount, totalAmount;

    if (calculationType.value === "exclusive") {
      // Tax-exclusive calculation (add GST to amount)
      originalAmount = amountValue;
      gstAmount = originalAmount * rateValue;
      totalAmount = originalAmount + gstAmount;
    } else {
      // Tax-inclusive calculation (extract GST from amount)
      totalAmount = amountValue;
      originalAmount = totalAmount / (1 + rateValue);
      gstAmount = totalAmount - originalAmount;
    }

    // Round values to 2 decimal places
    originalAmount = calculatorUtils.round(originalAmount, 2);
    gstAmount = calculatorUtils.round(gstAmount, 2);
    totalAmount = calculatorUtils.round(totalAmount, 2);

    // Display results
    document.getElementById("original-amount").textContent =
      calculatorUtils.formatCurrency(originalAmount);
    document.getElementById("gst-amount").textContent =
      calculatorUtils.formatCurrency(gstAmount);
    document.getElementById("total-amount").textContent =
      calculatorUtils.formatCurrency(totalAmount);
    results.style.display = "block";

    // Add visual content strategy components
    addGstVisualContent(
      calculationType.value,
      originalAmount,
      gstAmount,
      totalAmount,
      rateValue * 100,
    );

    // Save calculation to history
    storageManager.saveCalculationHistory("gst", {
      calculationType: calculationType.value,
      amount: amountValue,
      gstRate:
        gstRate.value === "custom"
          ? parseFloat(customRate.value)
          : parseFloat(gstRate.value) * 100,
      originalAmount: originalAmount,
      gstAmount: gstAmount,
      totalAmount: totalAmount,
    });
  });
}

/**
 * Initialize Income Tax Calculator
 */
function initIncomeTaxCalculator() {
  // Get form elements
  const form = document.getElementById("income-tax-calculator-form");
  const taxRegime = document.getElementById("tax-regime");
  const financialYear = document.getElementById("financial-year");
  const grossIncome = document.getElementById("gross-income");
  const standardDeduction = document.getElementById("standard-deduction");
  const section80C = document.getElementById("section-80c");
  const section80D = document.getElementById("section-80d");
  const otherDeductions = document.getElementById("other-deductions");
  const results = document.getElementById("income-tax-results");

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("income-tax");
  if (savedValues) {
    taxRegime.value = savedValues.taxRegime;
    financialYear.value = savedValues.financialYear;
    grossIncome.value = savedValues.grossIncome;

    if (savedValues.standardDeduction) {
      standardDeduction.value = savedValues.standardDeduction;
    }

    if (savedValues.section80C) {
      section80C.value = savedValues.section80C;
    }

    if (savedValues.section80D) {
      section80D.value = savedValues.section80D;
    }

    if (savedValues.otherDeductions) {
      otherDeductions.value = savedValues.otherDeductions;
    }
  }

  // Toggle deduction fields based on tax regime
  taxRegime.addEventListener("change", function () {
    const deductionsSection = document.getElementById("deductions-section");
    if (this.value === "old") {
      deductionsSection.style.display = "block";
    } else {
      deductionsSection.style.display = "none";
    }
  });

  // Trigger change event to set initial state
  const event = new Event("change");
  taxRegime.dispatchEvent(event);

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const taxRegimeValue = taxRegime.value;
    const financialYearValue = financialYear.value;
    const grossIncomeValue = parseFloat(grossIncome.value);
    const standardDeductionValue = parseFloat(standardDeduction.value) || 0;
    const section80CValue = parseFloat(section80C.value) || 0;
    const section80DValue = parseFloat(section80D.value) || 0;
    const otherDeductionsValue = parseFloat(otherDeductions.value) || 0;

    // Validate inputs
    const incomeValidation = calculatorUtils.validateNumericInput(
      grossIncomeValue,
      0,
      Number.MAX_SAFE_INTEGER,
      "Please enter a valid gross income",
    );

    if (!incomeValidation.valid) {
      calculatorUtils.showError(grossIncome, incomeValidation.message);
      return;
    }

    if (taxRegimeValue === "old") {
      // Validate deductions for old regime
      const standardDeductionValidation = calculatorUtils.validateNumericInput(
        standardDeductionValue,
        0,
        50000,
        "Standard deduction cannot exceed ₹50,000",
      );

      if (!standardDeductionValidation.valid) {
        calculatorUtils.showError(
          standardDeduction,
          standardDeductionValidation.message,
        );
        return;
      }

      const section80CValidation = calculatorUtils.validateNumericInput(
        section80CValue,
        0,
        150000,
        "Section 80C deduction cannot exceed ₹1,50,000",
      );

      if (!section80CValidation.valid) {
        calculatorUtils.showError(section80C, section80CValidation.message);
        return;
      }

      const section80DValidation = calculatorUtils.validateNumericInput(
        section80DValue,
        0,
        100000,
        "Section 80D deduction cannot exceed ₹1,00,000",
      );

      if (!section80DValidation.valid) {
        calculatorUtils.showError(section80D, section80DValidation.message);
        return;
      }

      const otherDeductionsValidation = calculatorUtils.validateNumericInput(
        otherDeductionsValue,
        0,
        300000,
        "Other deductions cannot exceed ₹3,00,000",
      );

      if (!otherDeductionsValidation.valid) {
        calculatorUtils.showError(
          otherDeductions,
          otherDeductionsValidation.message,
        );
        return;
      }
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("income-tax", {
      taxRegime: taxRegimeValue,
      financialYear: financialYearValue,
      grossIncome: grossIncomeValue,
      standardDeduction: standardDeductionValue,
      section80C: section80CValue,
      section80D: section80DValue,
      otherDeductions: otherDeductionsValue,
    });

    // Calculate taxable income
    let taxableIncome = grossIncomeValue;
    let totalDeductions = 0;

    if (taxRegimeValue === "old") {
      // Apply deductions for old regime
      totalDeductions =
        standardDeductionValue +
        section80CValue +
        section80DValue +
        otherDeductionsValue;
      taxableIncome = Math.max(0, grossIncomeValue - totalDeductions);
    }

    // Calculate income tax based on regime and financial year
    const taxDetails = calculateIncomeTax(
      taxableIncome,
      taxRegimeValue,
      financialYearValue,
    );

    // Round values to 2 decimal places
    const roundedTaxableIncome = calculatorUtils.round(taxableIncome, 2);
    const roundedIncomeTax = calculatorUtils.round(taxDetails.incomeTax, 2);
    const roundedCess = calculatorUtils.round(taxDetails.cess, 2);
    const roundedSurcharge = calculatorUtils.round(taxDetails.surcharge, 2);
    const roundedTotalTax = calculatorUtils.round(taxDetails.totalTax, 2);

    // Display results
    document.getElementById("gross-income-result").textContent =
      calculatorUtils.formatCurrency(grossIncomeValue);
    document.getElementById("total-deductions").textContent =
      calculatorUtils.formatCurrency(totalDeductions);
    document.getElementById("taxable-income").textContent =
      calculatorUtils.formatCurrency(roundedTaxableIncome);
    document.getElementById("income-tax").textContent =
      calculatorUtils.formatCurrency(roundedIncomeTax);
    document.getElementById("cess").textContent =
      calculatorUtils.formatCurrency(roundedCess);
    document.getElementById("surcharge").textContent =
      calculatorUtils.formatCurrency(roundedSurcharge);
    document.getElementById("total-tax").textContent =
      calculatorUtils.formatCurrency(roundedTotalTax);

    // Display tax slabs
    displayTaxSlabs(
      taxDetails.slabs,
      document.getElementById("tax-slabs-container"),
    );

    results.style.display = "block";

    // Add visual content strategy components
    addIncomeTaxVisualContent(
      grossIncomeValue,
      totalDeductions,
      roundedTaxableIncome,
      roundedTotalTax,
      taxRegimeValue,
      financialYearValue,
    );

    // Save calculation to history
    storageManager.saveCalculationHistory("income-tax", {
      taxRegime: taxRegimeValue,
      financialYear: financialYearValue,
      grossIncome: grossIncomeValue,
      totalDeductions: totalDeductions,
      taxableIncome: roundedTaxableIncome,
      incomeTax: roundedIncomeTax,
      cess: roundedCess,
      surcharge: roundedSurcharge,
      totalTax: roundedTotalTax,
    });
  });
}

/**
 * Calculate income tax based on regime, financial year, and taxable income
 */
function calculateIncomeTax(taxableIncome, regime, financialYear) {
  let incomeTax = 0;
  let slabs = [];

  // Define tax slabs based on regime and financial year
  if (regime === "old") {
    // Old regime tax slabs
    slabs = [
      { limit: 250000, rate: 0, tax: 0 },
      { limit: 500000, rate: 5, tax: 0 },
      { limit: 1000000, rate: 20, tax: 0 },
      { limit: Infinity, rate: 30, tax: 0 },
    ];
  } else {
    // New regime tax slabs (FY 2023-24)
    if (financialYear === "2023-24") {
      slabs = [
        { limit: 300000, rate: 0, tax: 0 },
        { limit: 600000, rate: 5, tax: 0 },
        { limit: 900000, rate: 10, tax: 0 },
        { limit: 1200000, rate: 15, tax: 0 },
        { limit: 1500000, rate: 20, tax: 0 },
        { limit: Infinity, rate: 30, tax: 0 },
      ];
    } else {
      // New regime tax slabs (FY 2022-23)
      slabs = [
        { limit: 250000, rate: 0, tax: 0 },
        { limit: 500000, rate: 5, tax: 0 },
        { limit: 750000, rate: 10, tax: 0 },
        { limit: 1000000, rate: 15, tax: 0 },
        { limit: 1250000, rate: 20, tax: 0 },
        { limit: 1500000, rate: 25, tax: 0 },
        { limit: Infinity, rate: 30, tax: 0 },
      ];
    }
  }

  // Calculate tax for each slab
  let remainingIncome = taxableIncome;
  let previousLimit = 0;

  for (let i = 0; i < slabs.length; i++) {
    const slab = slabs[i];
    const slabIncome = Math.min(remainingIncome, slab.limit - previousLimit);

    if (slabIncome <= 0) {
      break;
    }

    const slabTax = (slabIncome * slab.rate) / 100;
    slab.tax = slabTax;
    incomeTax += slabTax;

    remainingIncome -= slabIncome;
    previousLimit = slab.limit;

    if (remainingIncome <= 0) {
      break;
    }
  }

  // Calculate cess (4% of income tax)
  const cess = incomeTax * 0.04;

  // Calculate surcharge based on income
  let surcharge = 0;
  if (taxableIncome > 5000000 && taxableIncome <= 10000000) {
    surcharge = incomeTax * 0.1; // 10% surcharge
  } else if (taxableIncome > 10000000 && taxableIncome <= 20000000) {
    surcharge = incomeTax * 0.15; // 15% surcharge
  } else if (taxableIncome > 20000000 && taxableIncome <= 50000000) {
    surcharge = incomeTax * 0.25; // 25% surcharge
  } else if (taxableIncome > 50000000) {
    surcharge = incomeTax * 0.37; // 37% surcharge
  }

  // Calculate total tax
  const totalTax = incomeTax + cess + surcharge;

  return {
    incomeTax: incomeTax,
    cess: cess,
    surcharge: surcharge,
    totalTax: totalTax,
    slabs: slabs,
  };
}

/**
 * Display tax slabs in a table
 */
function displayTaxSlabs(slabs, container) {
  // Clear previous content
  container.innerHTML = "";

  // Create table
  const table = document.createElement("table");
  table.className = "tax-slabs-table";

  // Create table header
  const tableHeader = document.createElement("thead");
  tableHeader.innerHTML = `
    <tr>
      <th>Income Slab</th>
      <th>Tax Rate</th>
      <th>Tax Amount</th>
    </tr>
  `;
  table.appendChild(tableHeader);

  // Create table body
  const tableBody = document.createElement("tbody");

  let previousLimit = 0;

  // Add rows for each slab
  slabs.forEach((slab, index) => {
    if (slab.tax > 0) {
      const row = document.createElement("tr");

      // Income slab column
      const slabCell = document.createElement("td");
      if (slab.limit === Infinity) {
        slabCell.textContent = `Above ₹${(previousLimit / 100000).toFixed(
          2,
        )} Lakh`;
      } else {
        slabCell.textContent = `₹${(previousLimit / 100000).toFixed(
          2,
        )} Lakh to ₹${(slab.limit / 100000).toFixed(2)} Lakh`;
      }
      row.appendChild(slabCell);

      // Tax rate column
      const rateCell = document.createElement("td");
      rateCell.textContent = `${slab.rate}%`;
      row.appendChild(rateCell);

      // Tax amount column
      const taxCell = document.createElement("td");
      taxCell.textContent = calculatorUtils.formatCurrency(slab.tax);
      row.appendChild(taxCell);

      tableBody.appendChild(row);
    }

    previousLimit = slab.limit;
  });

  table.appendChild(tableBody);
  container.appendChild(table);
}

/**
 * Initialize Tax Comparison Tool
 */
function initTaxComparisonTool() {
  // Get form elements
  const form = document.getElementById("tax-comparison-form");
  const financialYear = document.getElementById("financial-year");
  const grossIncome = document.getElementById("gross-income");
  const standardDeduction = document.getElementById("standard-deduction");
  const section80C = document.getElementById("section-80c");
  const section80D = document.getElementById("section-80d");
  const otherDeductions = document.getElementById("other-deductions");
  const results = document.getElementById("tax-comparison-results");

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("tax-comparison");
  if (savedValues) {
    financialYear.value = savedValues.financialYear;
    grossIncome.value = savedValues.grossIncome;

    if (savedValues.standardDeduction) {
      standardDeduction.value = savedValues.standardDeduction;
    }

    if (savedValues.section80C) {
      section80C.value = savedValues.section80C;
    }

    if (savedValues.section80D) {
      section80D.value = savedValues.section80D;
    }

    if (savedValues.otherDeductions) {
      otherDeductions.value = savedValues.otherDeductions;
    }
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const financialYearValue = financialYear.value;
    const grossIncomeValue = parseFloat(grossIncome.value);
    const standardDeductionValue = parseFloat(standardDeduction.value) || 0;
    const section80CValue = parseFloat(section80C.value) || 0;
    const section80DValue = parseFloat(section80D.value) || 0;
    const otherDeductionsValue = parseFloat(otherDeductions.value) || 0;

    // Validate inputs
    const incomeValidation = calculatorUtils.validateNumericInput(
      grossIncomeValue,
      0,
      Number.MAX_SAFE_INTEGER,
      "Please enter a valid gross income",
    );

    if (!incomeValidation.valid) {
      calculatorUtils.showError(grossIncome, incomeValidation.message);
      return;
    }

    // Validate deductions
    const standardDeductionValidation = calculatorUtils.validateNumericInput(
      standardDeductionValue,
      0,
      50000,
      "Standard deduction cannot exceed ₹50,000",
    );

    if (!standardDeductionValidation.valid) {
      calculatorUtils.showError(
        standardDeduction,
        standardDeductionValidation.message,
      );
      return;
    }

    const section80CValidation = calculatorUtils.validateNumericInput(
      section80CValue,
      0,
      150000,
      "Section 80C deduction cannot exceed ₹1,50,000",
    );

    if (!section80CValidation.valid) {
      calculatorUtils.showError(section80C, section80CValidation.message);
      return;
    }

    const section80DValidation = calculatorUtils.validateNumericInput(
      section80DValue,
      0,
      100000,
      "Section 80D deduction cannot exceed ₹1,00,000",
    );

    if (!section80DValidation.valid) {
      calculatorUtils.showError(section80D, section80DValidation.message);
      return;
    }

    const otherDeductionsValidation = calculatorUtils.validateNumericInput(
      otherDeductionsValue,
      0,
      300000,
      "Other deductions cannot exceed ₹3,00,000",
    );

    if (!otherDeductionsValidation.valid) {
      calculatorUtils.showError(
        otherDeductions,
        otherDeductionsValidation.message,
      );
      return;
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("tax-comparison", {
      financialYear: financialYearValue,
      grossIncome: grossIncomeValue,
      standardDeduction: standardDeductionValue,
      section80C: section80CValue,
      section80D: section80DValue,
      otherDeductions: otherDeductionsValue,
    });

    // Calculate taxable income for old regime
    const totalDeductions =
      standardDeductionValue +
      section80CValue +
      section80DValue +
      otherDeductionsValue;
    const taxableIncomeOld = Math.max(0, grossIncomeValue - totalDeductions);

    // Calculate taxable income for new regime (no deductions)
    const taxableIncomeNew = grossIncomeValue;

    // Calculate tax for old regime
    const oldRegimeTax = calculateIncomeTax(
      taxableIncomeOld,
      "old",
      financialYearValue,
    );

    // Calculate tax for new regime
    const newRegimeTax = calculateIncomeTax(
      taxableIncomeNew,
      "new",
      financialYearValue,
    );

    // Determine which regime is better
    const betterRegime =
      oldRegimeTax.totalTax <= newRegimeTax.totalTax ? "old" : "new";
    const taxSaving = Math.abs(oldRegimeTax.totalTax - newRegimeTax.totalTax);

    // Round values to 2 decimal places
    const roundedTaxableIncomeOld = calculatorUtils.round(taxableIncomeOld, 2);
    const roundedTaxableIncomeNew = calculatorUtils.round(taxableIncomeNew, 2);
    const roundedTotalTaxOld = calculatorUtils.round(oldRegimeTax.totalTax, 2);
    const roundedTotalTaxNew = calculatorUtils.round(newRegimeTax.totalTax, 2);
    const roundedTaxSaving = calculatorUtils.round(taxSaving, 2);

    // Display results
    document.getElementById("gross-income-result").textContent =
      calculatorUtils.formatCurrency(grossIncomeValue);
    document.getElementById("total-deductions").textContent =
      calculatorUtils.formatCurrency(totalDeductions);

    document.getElementById("taxable-income-old").textContent =
      calculatorUtils.formatCurrency(roundedTaxableIncomeOld);
    document.getElementById("income-tax-old").textContent =
      calculatorUtils.formatCurrency(oldRegimeTax.incomeTax);
    document.getElementById("cess-old").textContent =
      calculatorUtils.formatCurrency(oldRegimeTax.cess);
    document.getElementById("surcharge-old").textContent =
      calculatorUtils.formatCurrency(oldRegimeTax.surcharge);
    document.getElementById("total-tax-old").textContent =
      calculatorUtils.formatCurrency(roundedTotalTaxOld);

    document.getElementById("taxable-income-new").textContent =
      calculatorUtils.formatCurrency(roundedTaxableIncomeNew);
    document.getElementById("income-tax-new").textContent =
      calculatorUtils.formatCurrency(newRegimeTax.incomeTax);
    document.getElementById("cess-new").textContent =
      calculatorUtils.formatCurrency(newRegimeTax.cess);
    document.getElementById("surcharge-new").textContent =
      calculatorUtils.formatCurrency(newRegimeTax.surcharge);
    document.getElementById("total-tax-new").textContent =
      calculatorUtils.formatCurrency(roundedTotalTaxNew);

    document.getElementById("better-regime").textContent =
      betterRegime === "old" ? "Old Regime" : "New Regime";
    document.getElementById("tax-saving").textContent =
      calculatorUtils.formatCurrency(roundedTaxSaving);

    // Generate and display comparison chart
    generateTaxComparisonChart(roundedTotalTaxOld, roundedTotalTaxNew);

    results.style.display = "block";

    // Save calculation to history
    storageManager.saveCalculationHistory("tax-comparison", {
      financialYear: financialYearValue,
      grossIncome: grossIncomeValue,
      totalDeductions: totalDeductions,
      taxableIncomeOld: roundedTaxableIncomeOld,
      taxableIncomeNew: roundedTaxableIncomeNew,
      totalTaxOld: roundedTotalTaxOld,
      totalTaxNew: roundedTotalTaxNew,
      betterRegime: betterRegime,
      taxSaving: roundedTaxSaving,
    });

    // Show in-article ad after calculation
    const inArticleAd = document.getElementById(
      "ad-between-calculator-results",
    );
    if (inArticleAd) {
      inArticleAd.style.display = "block";
    }
  });
}

/**
 * Generate comparison chart for Tax Comparison Tool
 */
function generateTaxComparisonChart(oldRegimeTax, newRegimeTax) {
  const chartContainer = document.getElementById(
    "tax-comparison-chart-container",
  );
  if (!chartContainer) return;

  chartContainer.innerHTML = ""; // Clear previous chart

  // Create chart container
  const chartEl = document.createElement("div");
  chartEl.className = "comparison-chart";

  // Create bars for Old Regime
  const oldRegimeBar = document.createElement("div");
  oldRegimeBar.className = "regime-bar";
  oldRegimeBar.innerHTML = `
    <div class="bar-label">Old Regime</div>
    <div class="bar-container">
      <div class="bar-segment old-regime-segment" style="height: 100%"></div>
    </div>
    <div class="bar-value">${calculatorUtils.formatCurrency(oldRegimeTax)}</div>
  `;

  // Create bars for New Regime
  const newRegimeBar = document.createElement("div");
  newRegimeBar.className = "regime-bar";
  newRegimeBar.innerHTML = `
    <div class="bar-label">New Regime</div>
    <div class="bar-container">
      <div class="bar-segment new-regime-segment" style="height: ${
        (newRegimeTax / Math.max(oldRegimeTax, newRegimeTax)) * 100
      }%"></div>
    </div>
    <div class="bar-value">${calculatorUtils.formatCurrency(newRegimeTax)}</div>
  `;

  // Assemble chart
  chartEl.appendChild(oldRegimeBar);
  chartEl.appendChild(newRegimeBar);

  chartContainer.appendChild(chartEl);
}

/**
 * Initialize Tax Refund Calculator
 */
function initTaxRefundCalculator() {
  // Get form elements
  const form = document.getElementById("tax-refund-calculator-form");
  const taxRegime = document.getElementById("tax-regime");
  const financialYear = document.getElementById("financial-year");
  const grossIncome = document.getElementById("gross-income");
  const taxAlreadyPaid = document.getElementById("tax-already-paid");
  const standardDeduction = document.getElementById("standard-deduction");
  const section80C = document.getElementById("section-80c");
  const section80D = document.getElementById("section-80d");
  const otherDeductions = document.getElementById("other-deductions");
  const deductionsSection = document.getElementById("deductions-section");
  const results = document.getElementById("tax-refund-results");

  // Load saved values if available
  const savedValues = storageManager.getCalculatorValues("tax-refund");
  if (savedValues) {
    taxRegime.value = savedValues.taxRegime || "old";
    financialYear.value = savedValues.financialYear || "2023-24";
    grossIncome.value = savedValues.grossIncome || "";
    taxAlreadyPaid.value = savedValues.taxAlreadyPaid || "";
    standardDeduction.value = savedValues.standardDeduction || 50000;
    section80C.value = savedValues.section80C || "";
    section80D.value = savedValues.section80D || "";
    otherDeductions.value = savedValues.otherDeductions || "";
  }

  // Toggle deductions section based on tax regime
  taxRegime.addEventListener("change", function () {
    if (this.value === "old") {
      deductionsSection.style.display = "block";
    } else {
      deductionsSection.style.display = "none";
    }
  });

  // Trigger change event to set initial state
  taxRegime.dispatchEvent(new Event("change"));

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Get values from form
    const taxRegimeValue = taxRegime.value;
    const financialYearValue = financialYear.value;
    const grossIncomeValue = parseFloat(grossIncome.value);
    const taxAlreadyPaidValue = parseFloat(taxAlreadyPaid.value);

    // Calculate deductions for old regime
    let totalDeductions = 0;
    if (taxRegimeValue === "old") {
      const standardDeductionValue = parseFloat(standardDeduction.value) || 0;
      const section80CValue = parseFloat(section80C.value) || 0;
      const section80DValue = parseFloat(section80D.value) || 0;
      const otherDeductionsValue = parseFloat(otherDeductions.value) || 0;

      totalDeductions =
        standardDeductionValue +
        section80CValue +
        section80DValue +
        otherDeductionsValue;
    } else {
      // New regime has no deductions
      totalDeductions = 0;
    }

    // Calculate taxable income
    const taxableIncome = Math.max(0, grossIncomeValue - totalDeductions);

    // Calculate tax liability based on regime and financial year
    let taxLiability = 0;
    if (taxRegimeValue === "old") {
      taxLiability = calculateOldRegimeTax(taxableIncome, financialYearValue);
    } else {
      taxLiability = calculateNewRegimeTax(taxableIncome, financialYearValue);
    }

    // Calculate refund or additional tax due
    const refundAmount = taxAlreadyPaidValue - taxLiability;

    // Display results
    document.getElementById("gross-income-result").textContent =
      calculatorUtils.formatCurrency(grossIncomeValue);
    document.getElementById("total-deductions").textContent =
      calculatorUtils.formatCurrency(totalDeductions);
    document.getElementById("taxable-income").textContent =
      calculatorUtils.formatCurrency(taxableIncome);
    document.getElementById("tax-liability").textContent =
      calculatorUtils.formatCurrency(taxLiability);
    document.getElementById("tax-paid-result").textContent =
      calculatorUtils.formatCurrency(taxAlreadyPaidValue);

    const refundElement = document.getElementById("tax-refund-amount");
    if (refundAmount >= 0) {
      refundElement.textContent = `Refund: ${calculatorUtils.formatCurrency(
        refundAmount,
      )}`;
      refundElement.classList.add("positive-result");
      refundElement.classList.remove("negative-result");
    } else {
      refundElement.textContent = `Additional Tax Due: ${calculatorUtils.formatCurrency(
        Math.abs(refundAmount),
      )}`;
      refundElement.classList.add("negative-result");
      refundElement.classList.remove("positive-result");
    }

    // Display tax slabs
    displayTaxSlabsForRefund(taxRegimeValue, financialYearValue, taxableIncome);

    // Show results
    results.style.display = "block";

    // Save values to localStorage
    storageManager.saveCalculatorValues("tax-refund", {
      taxRegime: taxRegimeValue,
      financialYear: financialYearValue,
      grossIncome: grossIncomeValue,
      taxAlreadyPaid: taxAlreadyPaidValue,
      standardDeduction: parseFloat(standardDeduction.value) || 0,
      section80C: parseFloat(section80C.value) || 0,
      section80D: parseFloat(section80D.value) || 0,
      otherDeductions: parseFloat(otherDeductions.value) || 0,
    });

    // Save calculation to history
    storageManager.saveCalculationHistory("tax-refund", {
      financialYear: financialYearValue,
      taxRegime: taxRegimeValue,
      grossIncome: grossIncomeValue,
      totalDeductions: totalDeductions,
      taxableIncome: taxableIncome,
      taxLiability: taxLiability,
      taxAlreadyPaid: taxAlreadyPaidValue,
      refundAmount: refundAmount,
    });

    // Show in-article ad after calculation
    const inArticleAd = document.getElementById(
      "ad-between-calculator-results",
    );
    if (inArticleAd) {
      inArticleAd.style.display = "block";
    }
  });
}

/**
 * Initialize Capital Gains Tax Calculator
 */
function initCapitalGainsCalculator() {
  // Get form elements
  const form = document.getElementById("capital-gains-calculator-form");
  const assetType = document.getElementById("asset-type");
  const holdingPeriod = document.getElementById("holding-period");
  const purchasePrice = document.getElementById("purchase-price");
  const sellingPrice = document.getElementById("selling-price");
  const purchaseDate = document.getElementById("purchase-date");
  const sellingDate = document.getElementById("selling-date");
  const expenses = document.getElementById("expenses");
  const incomeSlab = document.getElementById("income-slab");
  const results = document.getElementById("capital-gains-results");

  // Load saved values if available
  const savedValues = storageManager.getCalculatorValues("capital-gains");
  if (savedValues) {
    assetType.value = savedValues.assetType || "equity";
    holdingPeriod.value = savedValues.holdingPeriod || "short";
    purchasePrice.value = savedValues.purchasePrice || "";
    sellingPrice.value = savedValues.sellingPrice || "";
    purchaseDate.value = savedValues.purchaseDate || "";
    sellingDate.value = savedValues.sellingDate || "";
    expenses.value = savedValues.expenses || "";
    incomeSlab.value = savedValues.incomeSlab || "0.3";
  }

  // Update holding period automatically based on dates
  function updateHoldingPeriod() {
    if (purchaseDate.value && sellingDate.value) {
      const purchase = new Date(purchaseDate.value);
      const selling = new Date(sellingDate.value);
      const diffTime = Math.abs(selling - purchase);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      // Set holding period based on asset type and days
      if (assetType.value === "equity") {
        // For equity, long term is > 1 year
        holdingPeriod.value = diffDays > 365 ? "long" : "short";
      } else if (assetType.value === "property") {
        // For property, long term is > 2 years
        holdingPeriod.value = diffDays > 730 ? "long" : "short";
      } else {
        // For other assets, long term is > 3 years
        holdingPeriod.value = diffDays > 1095 ? "long" : "short";
      }
    }
  }

  // Add event listeners for date changes
  purchaseDate.addEventListener("change", updateHoldingPeriod);
  sellingDate.addEventListener("change", updateHoldingPeriod);
  assetType.addEventListener("change", updateHoldingPeriod);

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Get values from form
    const assetTypeValue = assetType.value;
    const holdingPeriodValue = holdingPeriod.value;
    const purchasePriceValue = parseFloat(purchasePrice.value);
    const sellingPriceValue = parseFloat(sellingPrice.value);
    const expensesValue = parseFloat(expenses.value) || 0;
    const incomeSlabValue = parseFloat(incomeSlab.value);

    // Calculate capital gain
    const capitalGain = sellingPriceValue - purchasePriceValue - expensesValue;

    // Determine tax rate based on asset type and holding period
    let taxRate = 0;
    if (holdingPeriodValue === "short") {
      if (assetTypeValue === "equity") {
        taxRate = 0.15; // 15% for short-term equity
      } else {
        taxRate = incomeSlabValue; // As per income slab for other short-term assets
      }
    } else {
      // Long-term capital gains
      if (assetTypeValue === "equity") {
        taxRate = 0.1; // 10% for long-term equity (above ₹1 lakh exemption)
      } else if (assetTypeValue === "property") {
        taxRate = 0.2; // 20% for long-term property with indexation
      } else {
        taxRate = 0.2; // 20% for other long-term assets
      }
    }

    // Calculate tax
    let capitalGainsTax = 0;
    if (capitalGain > 0) {
      // For long-term equity, exemption of ₹1 lakh
      if (assetTypeValue === "equity" && holdingPeriodValue === "long") {
        const taxableGain = Math.max(0, capitalGain - 100000);
        capitalGainsTax = taxableGain * taxRate;
      } else {
        capitalGainsTax = capitalGain * taxRate;
      }
    }

    // Display results
    document.getElementById("purchase-price-result").textContent =
      calculatorUtils.formatCurrency(purchasePriceValue);
    document.getElementById("selling-price-result").textContent =
      calculatorUtils.formatCurrency(sellingPriceValue);
    document.getElementById("expenses-result").textContent =
      calculatorUtils.formatCurrency(expensesValue);

    const capitalGainElement = document.getElementById("capital-gain");
    if (capitalGain >= 0) {
      capitalGainElement.textContent =
        calculatorUtils.formatCurrency(capitalGain);
      capitalGainElement.classList.add("positive-result");
      capitalGainElement.classList.remove("negative-result");
    } else {
      capitalGainElement.textContent = `Loss: ${calculatorUtils.formatCurrency(
        Math.abs(capitalGain),
      )}`;
      capitalGainElement.classList.add("negative-result");
      capitalGainElement.classList.remove("positive-result");
    }

    document.getElementById("tax-rate").textContent = `${(
      taxRate * 100
    ).toFixed(1)}%`;
    document.getElementById("capital-gains-tax").textContent =
      calculatorUtils.formatCurrency(capitalGainsTax);

    // Show results
    results.style.display = "block";

    // Save values to localStorage
    storageManager.saveCalculatorValues("capital-gains", {
      assetType: assetTypeValue,
      holdingPeriod: holdingPeriodValue,
      purchasePrice: purchasePriceValue,
      sellingPrice: sellingPriceValue,
      purchaseDate: purchaseDate.value,
      sellingDate: sellingDate.value,
      expenses: expensesValue,
      incomeSlab: incomeSlab.value,
    });

    // Save calculation to history
    storageManager.saveCalculationHistory("capital-gains", {
      assetType: assetTypeValue,
      holdingPeriod: holdingPeriodValue,
      purchasePrice: purchasePriceValue,
      sellingPrice: sellingPriceValue,
      expenses: expensesValue,
      capitalGain: capitalGain,
      taxRate: taxRate,
      capitalGainsTax: capitalGainsTax,
    });

    // Show in-article ad after calculation
    const inArticleAd = document.getElementById(
      "ad-between-calculator-results",
    );
    if (inArticleAd) {
      inArticleAd.style.display = "block";
    }
  });
}

/**
 * Calculate tax for old regime
 * @param {number} taxableIncome - Taxable income
 * @param {string} financialYear - Financial year
 * @returns {number} - Tax liability
 */
function calculateOldRegimeTax(taxableIncome, financialYear) {
  let tax = 0;

  // Tax slabs for old regime (2023-24)
  if (financialYear === "2023-24" || financialYear === "2022-23") {
    if (taxableIncome <= 250000) {
      tax = 0;
    } else if (taxableIncome <= 500000) {
      tax = (taxableIncome - 250000) * 0.05;
    } else if (taxableIncome <= 1000000) {
      tax = 12500 + (taxableIncome - 500000) * 0.2;
    } else {
      tax = 112500 + (taxableIncome - 1000000) * 0.3;
    }
  }

  // Add cess (4%)
  tax = tax * 1.04;

  return tax;
}

/**
 * Calculate tax for new regime
 * @param {number} taxableIncome - Taxable income
 * @param {string} financialYear - Financial year
 * @returns {number} - Tax liability
 */
function calculateNewRegimeTax(taxableIncome, financialYear) {
  let tax = 0;

  // Tax slabs for new regime (2023-24)
  if (financialYear === "2023-24") {
    if (taxableIncome <= 300000) {
      tax = 0;
    } else if (taxableIncome <= 600000) {
      tax = (taxableIncome - 300000) * 0.05;
    } else if (taxableIncome <= 900000) {
      tax = 15000 + (taxableIncome - 600000) * 0.1;
    } else if (taxableIncome <= 1200000) {
      tax = 45000 + (taxableIncome - 900000) * 0.15;
    } else if (taxableIncome <= 1500000) {
      tax = 90000 + (taxableIncome - 1200000) * 0.2;
    } else {
      tax = 150000 + (taxableIncome - 1500000) * 0.3;
    }
  } else if (financialYear === "2022-23") {
    // 2022-23 new regime slabs
    if (taxableIncome <= 250000) {
      tax = 0;
    } else if (taxableIncome <= 500000) {
      tax = (taxableIncome - 250000) * 0.05;
    } else if (taxableIncome <= 750000) {
      tax = 12500 + (taxableIncome - 500000) * 0.1;
    } else if (taxableIncome <= 1000000) {
      tax = 37500 + (taxableIncome - 750000) * 0.15;
    } else if (taxableIncome <= 1250000) {
      tax = 75000 + (taxableIncome - 1000000) * 0.2;
    } else if (taxableIncome <= 1500000) {
      tax = 125000 + (taxableIncome - 1250000) * 0.25;
    } else {
      tax = 187500 + (taxableIncome - 1500000) * 0.3;
    }
  }

  // Add cess (4%)
  tax = tax * 1.04;

  return tax;
}

/**
 * Display tax slabs in the results for tax refund calculator
 * @param {string} regime - Tax regime (old/new)
 * @param {string} financialYear - Financial year
 * @param {number} taxableIncome - Taxable income
 */
function displayTaxSlabsForRefund(regime, financialYear, taxableIncome) {
  const container = document.getElementById("tax-slabs-container");
  container.innerHTML = "";

  const heading = document.createElement("h4");
  heading.textContent = `${
    regime === "old" ? "Old" : "New"
  } Regime Tax Slabs (${financialYear})`;
  container.appendChild(heading);

  const table = document.createElement("table");
  table.className = "tax-slabs-table";

  // Create table header
  const thead = document.createElement("thead");
  const headerRow = document.createElement("tr");
  ["Income Slab", "Tax Rate", "Your Tax"].forEach((text) => {
    const th = document.createElement("th");
    th.textContent = text;
    headerRow.appendChild(th);
  });
  thead.appendChild(headerRow);
  table.appendChild(thead);

  // Create table body
  const tbody = document.createElement("tbody");

  // Add rows based on regime and financial year
  let slabs = [];
  if (regime === "old") {
    slabs = [
      { min: 0, max: 250000, rate: "0%" },
      { min: 250000, max: 500000, rate: "5%" },
      { min: 500000, max: 1000000, rate: "20%" },
      { min: 1000000, max: Infinity, rate: "30%" },
    ];
  } else {
    if (financialYear === "2023-24") {
      slabs = [
        { min: 0, max: 300000, rate: "0%" },
        { min: 300000, max: 600000, rate: "5%" },
        { min: 600000, max: 900000, rate: "10%" },
        { min: 900000, max: 1200000, rate: "15%" },
        { min: 1200000, max: 1500000, rate: "20%" },
        { min: 1500000, max: Infinity, rate: "30%" },
      ];
    } else if (financialYear === "2022-23") {
      slabs = [
        { min: 0, max: 250000, rate: "0%" },
        { min: 250000, max: 500000, rate: "5%" },
        { min: 500000, max: 750000, rate: "10%" },
        { min: 750000, max: 1000000, rate: "15%" },
        { min: 1000000, max: 1250000, rate: "20%" },
        { min: 1250000, max: 1500000, rate: "25%" },
        { min: 1500000, max: Infinity, rate: "30%" },
      ];
    }
  }

  // Calculate tax for each slab
  let remainingIncome = taxableIncome;
  let previousLimit = 0;
  let totalTax = 0;

  for (let i = 0; i < slabs.length; i++) {
    const slab = slabs[i];
    const slabIncome = Math.min(remainingIncome, slab.max - previousLimit);

    if (slabIncome <= 0) {
      break;
    }

    const slabTax = (slabIncome * parseFloat(slab.rate)) / 100;
    totalTax += slabTax;

    // Create table row
    const row = document.createElement("tr");

    // Income slab column
    const slabCell = document.createElement("td");
    if (slab.max === Infinity) {
      slabCell.textContent = `Above ₹${(previousLimit / 100000).toFixed(
        2,
      )} Lakh`;
    } else {
      slabCell.textContent = `₹${(previousLimit / 100000).toFixed(
        2,
      )} Lakh to ₹${(slab.max / 100000).toFixed(2)} Lakh`;
    }
    row.appendChild(slabCell);

    // Tax rate column
    const rateCell = document.createElement("td");
    rateCell.textContent = slab.rate;
    row.appendChild(rateCell);

    // Tax amount column
    const taxCell = document.createElement("td");
    taxCell.textContent = calculatorUtils.formatCurrency(slabTax);
    row.appendChild(taxCell);

    tbody.appendChild(row);

    remainingIncome -= slabIncome;
    previousLimit = slab.max;
  }

  table.appendChild(tbody);
  container.appendChild(table);
}

/**
 * Add visual content strategy components to GST calculator
 */
function addGstVisualContent(
  calculationType,
  originalAmount,
  gstAmount,
  totalAmount,
  gstRate,
) {
  // Add GST breakdown micro-infographic
  addGstMicroInfographic(
    calculationType,
    originalAmount,
    gstAmount,
    totalAmount,
    gstRate,
  );

  // Add restaurant bill comparison
  addRestaurantBillComparison(originalAmount, gstAmount);

  // Add GST rate comparison chart
  addGstRateComparison(originalAmount);

  // Add process infographic
  addGstProcessInfographic();
}

/**
 * Create GST micro-infographic for social sharing
 */
function addGstMicroInfographic(
  calculationType,
  originalAmount,
  gstAmount,
  totalAmount,
  gstRate,
) {
  let container = document.getElementById("gst-micro-container");
  if (!container) {
    container = document.createElement("div");
    container.id = "gst-micro-container";

    // Insert after results section
    const resultsSection = document.getElementById("gst-results");
    if (resultsSection && resultsSection.parentNode) {
      resultsSection.parentNode.insertBefore(
        container,
        resultsSection.nextSibling,
      );
    }
  }

  const hook =
    calculationType === "exclusive" ? "GST Added!" : "GST Extracted!";
  const content = `${gstRate}% GST on ₹${originalAmount.toLocaleString()}`;

  if (typeof VisualComponents !== "undefined") {
    VisualComponents.createMicroInfographic({
      containerId: "gst-micro-container",
      theme: "gst",
      hook: hook,
      content: content,
      stats: [
        {
          value: `₹${gstAmount.toLocaleString()}`,
          label: "GST Amount",
        },
        {
          value: `₹${totalAmount.toLocaleString()}`,
          label: "Total Amount",
        },
      ],
    });
  }
}

/**
 * Create restaurant bill comparison infographic
 */
function addRestaurantBillComparison(originalAmount, gstAmount) {
  let container = document.getElementById("restaurant-comparison-container");
  if (!container) {
    container = document.createElement("div");
    container.id = "restaurant-comparison-container";

    // Insert after micro-infographic section
    const microSection = document.getElementById("gst-micro-container");
    if (microSection && microSection.parentNode) {
      microSection.parentNode.insertBefore(container, microSection.nextSibling);
    }
  }

  // Calculate different scenarios
  const dineInGst = originalAmount * 0.18; // 18% for dine-in
  const takeawayGst = originalAmount * 0.05; // 5% for takeaway
  const dineInTotal = originalAmount + dineInGst;
  const takeawayTotal = originalAmount + takeawayGst;

  if (typeof VisualComponents !== "undefined") {
    VisualComponents.createComparisonInfographic({
      containerId: "restaurant-comparison-container",
      title: "Restaurant Bill: Dine-in vs Takeaway GST",
      leftSide: {
        theme: "gst",
        icon: "🍽️",
        title: "Dine-in (18% GST)",
        metrics: [
          {
            label: "Food Amount",
            value: calculatorUtils.formatCurrency(originalAmount),
          },
          {
            label: "GST (18%)",
            value: calculatorUtils.formatCurrency(dineInGst),
          },
          {
            label: "Total Bill",
            value: calculatorUtils.formatCurrency(dineInTotal),
          },
        ],
      },
      rightSide: {
        theme: "gst",
        icon: "🥡",
        title: "Takeaway (5% GST)",
        metrics: [
          {
            label: "Food Amount",
            value: calculatorUtils.formatCurrency(originalAmount),
          },
          {
            label: "GST (5%)",
            value: calculatorUtils.formatCurrency(takeawayGst),
          },
          {
            label: "Total Bill",
            value: calculatorUtils.formatCurrency(takeawayTotal),
          },
        ],
      },
      insight: `Same food, different GST rates! You save ₹${(
        dineInTotal - takeawayTotal
      ).toFixed(2)} by choosing takeaway over dine-in.`,
    });
  }
}

/**
 * Create GST rate comparison chart
 */
function addGstRateComparison(baseAmount) {
  let container = document.getElementById("gst-rate-comparison-container");
  if (!container) {
    container = document.createElement("div");
    container.id = "gst-rate-comparison-container";

    // Insert after restaurant comparison section
    const restaurantSection = document.getElementById(
      "restaurant-comparison-container",
    );
    if (restaurantSection && restaurantSection.parentNode) {
      restaurantSection.parentNode.insertBefore(
        container,
        restaurantSection.nextSibling,
      );
    }
  }

  // Calculate GST for different rates
  const rates = [
    { rate: 5, category: "Essential Items" },
    { rate: 12, category: "Processed Foods" },
    { rate: 18, category: "Most Services" },
    { rate: 28, category: "Luxury Items" },
  ];

  const chartData = {
    labels: rates.map((r) => `${r.rate}%`),
    values: rates.map((r) => baseAmount * (r.rate / 100)),
    colors: ["#38b000", "#4cc9f0", "#4361ee", "#f72585"],
    categories: rates.map((r) => r.category),
  };

  if (typeof VisualComponents !== "undefined") {
    VisualComponents.createEnhancedChart({
      containerId: "gst-rate-comparison-container",
      title: "GST Amount Across Different Tax Slabs",
      chartType: "bar",
      data: {
        labels: chartData.labels,
        values: chartData.values,
        colors: chartData.colors,
        datasets: [{ label: "GST Amount", colorClass: "gst" }],
      },
    });
  }
}

/**
 * Create GST process infographic
 */
function addGstProcessInfographic() {
  let container = document.getElementById("gst-process-container");
  if (!container) {
    container = document.createElement("div");
    container.id = "gst-process-container";

    // Insert after rate comparison section
    const rateSection = document.getElementById(
      "gst-rate-comparison-container",
    );
    if (rateSection && rateSection.parentNode) {
      rateSection.parentNode.insertBefore(container, rateSection.nextSibling);
    }
  }

  const html = `
    <div class="process-infographic">
      <h3 class="process-title">4 Steps to Calculate GST Correctly</h3>
      <div class="process-steps">
        <div class="process-step fade-in-up">
          <div class="process-step-number">1</div>
          <div class="process-step-icon">🎯</div>
          <h4 class="process-step-title">Identify Calculation Type</h4>
          <p class="process-step-description">Determine if you need to add GST to a base amount (tax-exclusive) or extract GST from a total amount (tax-inclusive).</p>
          <button class="process-step-action" onclick="document.getElementById('calculation-type').focus()">Select Type</button>
        </div>
        <div class="process-step fade-in-up">
          <div class="process-step-number">2</div>
          <div class="process-step-icon">💰</div>
          <h4 class="process-step-title">Enter Amount</h4>
          <p class="process-step-description">Input the base amount (for tax-exclusive) or total amount (for tax-inclusive) for which you want to calculate GST.</p>
          <button class="process-step-action" onclick="document.getElementById('amount').focus()">Enter Amount</button>
        </div>
        <div class="process-step fade-in-up">
          <div class="process-step-number">3</div>
          <div class="process-step-icon">📊</div>
          <h4 class="process-step-title">Select GST Rate</h4>
          <p class="process-step-description">Choose the appropriate GST rate: 5% (essentials), 12% (processed foods), 18% (services), 28% (luxury), or enter custom rate.</p>
          <button class="process-step-action" onclick="document.getElementById('gst-rate').focus()">Select Rate</button>
        </div>
        <div class="process-step fade-in-up">
          <div class="process-step-number">4</div>
          <div class="process-step-icon">🧮</div>
          <h4 class="process-step-title">Calculate & Verify</h4>
          <p class="process-step-description">Get instant results showing original amount, GST amount, and total. Verify the calculation matches your invoice requirements.</p>
          <button class="process-step-action" onclick="document.querySelector('.calculate-btn').click()">Calculate</button>
        </div>
      </div>
    </div>
  `;

  container.innerHTML = html;
}

/**
 * Add visual content strategy components to Income Tax calculator
 */
function addIncomeTaxVisualContent(
  grossIncome,
  totalDeductions,
  taxableIncome,
  totalTax,
  regime,
  financialYear,
) {
  // Add interactive tax regime comparison slider
  addTaxRegimeComparisonSlider(grossIncome, totalDeductions);

  // Add tax planning micro-infographic
  addTaxPlanningMicroInfographic(grossIncome, totalTax, regime);

  // Add tax optimization process infographic
  addTaxOptimizationProcess();
}

/**
 * Create interactive tax regime comparison slider
 */
function addTaxRegimeComparisonSlider(grossIncome, totalDeductions) {
  let container = document.getElementById("tax-regime-slider-container");
  if (!container) {
    container = document.createElement("div");
    container.id = "tax-regime-slider-container";

    // Insert after results section
    const resultsSection = document.getElementById("income-tax-results");
    if (resultsSection && resultsSection.parentNode) {
      resultsSection.parentNode.insertBefore(
        container,
        resultsSection.nextSibling,
      );
    }
  }

  if (typeof VisualComponents !== "undefined") {
    VisualComponents.createInteractiveSlider({
      containerId: "tax-regime-slider-container",
      title: "Tax Regime Comparison: Find Your Optimal Choice",
      sliders: [
        {
          label: "Annual Salary (₹)",
          min: 300000,
          max: 5000000,
          step: 50000,
          value: grossIncome || 800000,
          format: "currency",
        },
        {
          label: "80C Investments (₹)",
          min: 0,
          max: 150000,
          step: 10000,
          value: Math.min(totalDeductions * 0.7, 150000) || 100000,
          format: "currency",
        },
      ],
      onUpdate: function (values, resultsContainerId) {
        const [salary, investments] = values;

        // Calculate old regime tax
        const standardDeduction = 50000;
        const totalOldDeductions = standardDeduction + investments;
        const taxableIncomeOld = Math.max(0, salary - totalOldDeductions);
        const oldRegimeTax = calculateIncomeTax(
          taxableIncomeOld,
          "old",
          "2023-24",
        );

        // Calculate new regime tax
        const newRegimeTax = calculateIncomeTax(salary, "new", "2023-24");

        // Determine better regime
        const betterRegime =
          oldRegimeTax.totalTax <= newRegimeTax.totalTax
            ? "Old Regime"
            : "New Regime";
        const savings = Math.abs(oldRegimeTax.totalTax - newRegimeTax.totalTax);

        // Update results
        const resultsContainer = document.getElementById(resultsContainerId);
        if (resultsContainer) {
          resultsContainer.innerHTML = `
            <div class="slider-result-item">
              <span class="slider-result-label">Old Regime Tax</span>
              <span class="slider-result-value">${calculatorUtils.formatCurrency(
                oldRegimeTax.totalTax,
              )}</span>
            </div>
            <div class="slider-result-item">
              <span class="slider-result-label">New Regime Tax</span>
              <span class="slider-result-value">${calculatorUtils.formatCurrency(
                newRegimeTax.totalTax,
              )}</span>
            </div>
            <div class="slider-result-item slider-result-highlight">
              <span class="slider-result-label">Recommended: ${betterRegime}</span>
              <span class="slider-result-value">Save ₹${savings.toLocaleString()}</span>
            </div>
            <div class="slider-result-item">
              <span class="slider-result-label">Break-even Investment</span>
              <span class="slider-result-value">₹${calculateBreakEvenInvestment(
                salary,
              ).toLocaleString()}</span>
            </div>
          `;
        }
      },
    });
  }
}

/**
 * Calculate break-even investment amount for tax regime comparison
 */
function calculateBreakEvenInvestment(salary) {
  // Binary search to find break-even point
  let low = 0;
  let high = 150000;
  let breakEven = 0;

  while (low <= high) {
    const mid = Math.floor((low + high) / 2);
    const standardDeduction = 50000;
    const totalDeductions = standardDeduction + mid;
    const taxableIncomeOld = Math.max(0, salary - totalDeductions);

    const oldRegimeTax = calculateIncomeTax(taxableIncomeOld, "old", "2023-24");
    const newRegimeTax = calculateIncomeTax(salary, "new", "2023-24");

    if (Math.abs(oldRegimeTax.totalTax - newRegimeTax.totalTax) < 1000) {
      breakEven = mid;
      break;
    } else if (oldRegimeTax.totalTax > newRegimeTax.totalTax) {
      low = mid + 1;
    } else {
      high = mid - 1;
      breakEven = mid;
    }
  }

  return breakEven;
}

/**
 * Create tax planning micro-infographic
 */
function addTaxPlanningMicroInfographic(grossIncome, totalTax, regime) {
  let container = document.getElementById("tax-planning-micro-container");
  if (!container) {
    container = document.createElement("div");
    container.id = "tax-planning-micro-container";

    // Insert after slider section
    const sliderSection = document.getElementById(
      "tax-regime-slider-container",
    );
    if (sliderSection && sliderSection.parentNode) {
      sliderSection.parentNode.insertBefore(
        container,
        sliderSection.nextSibling,
      );
    }
  }

  const taxRate = ((totalTax / grossIncome) * 100).toFixed(1);
  const hook =
    regime === "old" ? "Smart Tax Planning!" : "Simplified Taxation!";
  const content = `₹${(grossIncome / 100000).toFixed(
    1,
  )}L income, ${taxRate}% effective tax rate`;

  if (typeof VisualComponents !== "undefined") {
    VisualComponents.createMicroInfographic({
      containerId: "tax-planning-micro-container",
      theme: "gst",
      hook: hook,
      content: content,
      stats: [
        {
          value: `₹${totalTax.toLocaleString()}`,
          label: "Annual Tax Liability",
        },
        {
          value: `${regime === "old" ? "Old" : "New"} Regime`,
          label: "Optimal Choice",
        },
      ],
    });
  }
}

/**
 * Create tax optimization process infographic
 */
function addTaxOptimizationProcess() {
  let container = document.getElementById("tax-optimization-process-container");
  if (!container) {
    container = document.createElement("div");
    container.id = "tax-optimization-process-container";

    // Insert after micro-infographic section
    const microSection = document.getElementById(
      "tax-planning-micro-container",
    );
    if (microSection && microSection.parentNode) {
      microSection.parentNode.insertBefore(container, microSection.nextSibling);
    }
  }

  const html = `
    <div class="process-infographic">
      <h3 class="process-title">5 Steps to Optimize Your Tax Planning</h3>
      <div class="process-steps">
        <div class="process-step fade-in-up">
          <div class="process-step-number">1</div>
          <div class="process-step-icon">📊</div>
          <h4 class="process-step-title">Calculate Both Regimes</h4>
          <p class="process-step-description">Use our calculator to compare tax liability under both old and new regimes with your actual income and deductions.</p>
          <button class="process-step-action" onclick="document.getElementById('gross-income').focus()">Enter Income</button>
        </div>
        <div class="process-step fade-in-up">
          <div class="process-step-number">2</div>
          <div class="process-step-icon">💰</div>
          <h4 class="process-step-title">Maximize 80C Investments</h4>
          <p class="process-step-description">Invest up to ₹1.5 lakh in ELSS, PPF, NSC, or life insurance to reduce taxable income under the old regime.</p>
          <button class="process-step-action" onclick="document.getElementById('section-80c').focus()">Add 80C</button>
        </div>
        <div class="process-step fade-in-up">
          <div class="process-step-number">3</div>
          <div class="process-step-icon">🏥</div>
          <h4 class="process-step-title">Health Insurance Benefits</h4>
          <p class="process-step-description">Claim up to ₹25,000 (₹50,000 for senior citizens) under Section 80D for health insurance premiums.</p>
          <button class="process-step-action" onclick="document.getElementById('section-80d').focus()">Add 80D</button>
        </div>
        <div class="process-step fade-in-up">
          <div class="process-step-number">4</div>
          <div class="process-step-icon">🏠</div>
          <h4 class="process-step-title">Home Loan Benefits</h4>
          <p class="process-step-description">Claim home loan interest up to ₹2 lakh under Section 24(b) and principal repayment under 80C for additional savings.</p>
          <button class="process-step-action" onclick="document.getElementById('other-deductions').focus()">Add Home Loan</button>
        </div>
        <div class="process-step fade-in-up">
          <div class="process-step-number">5</div>
          <div class="process-step-icon">📈</div>
          <h4 class="process-step-title">Plan & Invest Wisely</h4>
          <p class="process-step-description">Choose investments that offer tax benefits and good returns. Review annually and adjust based on income changes.</p>
          <button class="process-step-action" onclick="window.open('../investment/sip-calculator.html', '_blank')">Explore ELSS</button>
        </div>
      </div>
    </div>
  `;

  container.innerHTML = html;
}
